[package]
name = "helius_monitor"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = { version = "0.20", features = ["native-tls"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
url = "2.4"
futures-util = "0.3"
chrono = { version = "0.4", features = ["serde"] }
log = "0.4"
env_logger = "0.10"
dotenv = "0.15"
regex = "1.10"

[[bin]]
name = "helius_monitor"
path = "src/main.rs"
