use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};
use futures_util::{SinkExt, StreamExt};
use serde_json::Value;
use url::Url;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // WebSocket server URL
    let url = "ws://127.0.0.1:9005";
    
    println!("Connecting to WebSocket server at {}", url);
    
    // Parse the URL
    let url = Url::parse(url)?;
    
    // Connect to the WebSocket server
    let (ws_stream, _) = connect_async(url).await?;
    println!("WebSocket handshake has been completed");
    
    // Split the WebSocket stream into sender and receiver
    let (mut write, mut read) = ws_stream.split();
    
    // Spawn a task to handle incoming messages
    let read_task = tokio::spawn(async move {
        while let Some(msg) = read.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    println!("Received text message: {}", text);
                    
                    // Try to parse as JSON
                    match serde_json::from_str::<Value>(&text) {
                        Ok(json) => {
                            println!("Parsed JSON: {}", serde_json::to_string_pretty(&json).unwrap());

                            //// helius android websocket filtering 
                        }
                        Err(e) => {
                            println!("Failed to parse as JSON: {}", e);
                        }
                    }
                }
                Ok(Message::Binary(data)) => {
                    println!("Received binary message of {} bytes", data.len());
                    
                    // Try to parse binary data as JSON (assuming it's UTF-8 encoded)
                    if let Ok(text) = String::from_utf8(data) {
                        match serde_json::from_str::<Value>(&text) {
                            Ok(json) => {
                                println!("Parsed JSON from binary: {}", serde_json::to_string_pretty(&json).unwrap());
                            }
                            Err(e) => {
                                println!("Failed to parse binary as JSON: {}", e);
                            }
                        }
                    }
                }
                Ok(Message::Ping(data)) => {
                    println!("Received ping with {} bytes", data.len());
                }
                Ok(Message::Pong(data)) => {
                    println!("Received pong with {} bytes", data.len());
                }
                Ok(Message::Close(frame)) => {
                    println!("Received close frame: {:?}", frame);
                    break;
                }
                Ok(Message::Frame(_)) => {
                    // Raw frame, usually not handled at this level
                }
                Err(e) => {
                    println!("Error receiving message: {}", e);
                    break;
                }
            }
        }
    });
    
    // Spawn a task to send messages (optional - for testing)
    let write_task = tokio::spawn(async move {
        // Send a test message
        let test_message = serde_json::json!({
            "type": "hello",
            "message": "Hello from Rust WebSocket client!",
            "timestamp": chrono::Utc::now().timestamp()
        });
        
        let message = Message::Text(test_message.to_string());
        if let Err(e) = write.send(message).await {
            println!("Error sending message: {}", e);
        }
        
        // Keep the connection alive for a while
        tokio::time::sleep(tokio::time::Duration::from_secs(30)).await;
        
        // Send close frame
        if let Err(e) = write.send(Message::Close(None)).await {
            println!("Error sending close frame: {}", e);
        }
    });
    
    // Wait for both tasks to complete
    let (read_result, write_result) = tokio::join!(read_task, write_task);
    
    if let Err(e) = read_result {
        println!("Read task error: {}", e);
    }
    
    if let Err(e) = write_result {
        println!("Write task error: {}", e);
    }
    
    println!("WebSocket connection closed");
    Ok(())
}