use tokio::sync::mpsc;
use serde_json::{json, Value};
use std::sync::Arc;
use std::time::Duration;
use tokio::time::{sleep, timeout};

use crate::{
    JitoEvent, SubscriptionCommand, SubscriptionManager,
    parse_jito_event, create_logs_subscribe_message, create_logs_unsubscribe_message
};

/// Smoke test runner for the Jito event handling pipeline
pub struct SmokeTestRunner {
    pub subscription_manager: Arc<tokio::sync::Mutex<SubscriptionManager>>,
    pub jito_tx: mpsc::UnboundedSender<SubscriptionCommand>,
    pub mock_helius_rx: mpsc::UnboundedReceiver<String>,
}

impl SmokeTestRunner {
    /// Create a new smoke test runner with all necessary components
    pub fn new() -> (Self, mpsc::UnboundedReceiver<SubscriptionCommand>, mpsc::UnboundedSender<String>) {
        let (jito_tx, jito_rx) = mpsc::unbounded_channel::<SubscriptionCommand>();
        let (mock_helius_tx, mock_helius_rx) = mpsc::unbounded_channel::<String>();
        let subscription_manager = Arc::new(tokio::sync::Mutex::new(SubscriptionManager::new()));
        
        (
            Self {
                subscription_manager,
                jito_tx,
                mock_helius_rx,
            },
            jito_rx,
            mock_helius_tx,
        )
    }
    
    /// Simulate receiving a Jito event and verify the pipeline response
    pub async fn simulate_jito_event(&self, event_json: Value) -> Result<Option<JitoEvent>, String> {
        println!("🧪 Simulating Jito event: {}", event_json);
        
        // Parse the event
        let jito_event = parse_jito_event(&event_json);
        
        if let Some(ref event) = jito_event {
            // Send the appropriate command based on the event
            let command = match event {
                JitoEvent::NewToken { mint } => SubscriptionCommand::Subscribe { mint: mint.clone() },
                JitoEvent::DevSold { mint } => SubscriptionCommand::Unsubscribe { mint: mint.clone() },
            };
            
            if let Err(e) = self.jito_tx.send(command) {
                return Err(format!("Failed to send command: {}", e));
            }
            
            println!("✅ Event parsed and command sent successfully");
        } else {
            println!("⚠️  Event could not be parsed");
        }
        
        Ok(jito_event)
    }
    
    /// Wait for and verify a Helius subscription message
    pub async fn verify_helius_message(&mut self, expected_method: &str, expected_mint: Option<&str>) -> Result<Value, String> {
        let message = timeout(Duration::from_secs(5), self.mock_helius_rx.recv())
            .await
            .map_err(|_| "Timeout waiting for Helius message")?
            .ok_or("Channel closed")?;
        
        let json: Value = serde_json::from_str(&message)
            .map_err(|e| format!("Failed to parse Helius message: {}", e))?;
        
        // Verify the method
        let method = json["method"].as_str()
            .ok_or("Missing method in Helius message")?;
        
        if method != expected_method {
            return Err(format!("Expected method '{}', got '{}'", expected_method, method));
        }
        
        // Verify mint address for subscribe messages
        if let Some(mint) = expected_mint {
            if method == "transactionSubscribe" {
                let account_include = json["params"][0]["accountInclude"].as_array()
                    .ok_or("Missing accountInclude in subscribe message")?;

                if account_include.len() != 1 || account_include[0].as_str() != Some(mint) {
                    return Err(format!("Expected mint '{}' in accountInclude, got {:?}", mint, account_include));
                }
            }
        }
        
        println!("✅ Verified Helius message: method={}, mint={:?}", method, expected_mint);
        Ok(json)
    }
    
    /// Get the current subscription state for debugging
    pub async fn get_subscription_state(&self) -> Vec<String> {
        let manager = self.subscription_manager.lock().await;
        manager.active_subscriptions.keys().cloned().collect()
    }
}

/// Run a comprehensive smoke test of the Jito event handling pipeline
pub async fn run_smoke_test() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Starting Jito Event Pipeline Smoke Test");
    println!("{}", "=".repeat(50));
    
    let (mut test_runner, mut jito_rx, mock_helius_tx) = SmokeTestRunner::new();
    let subscription_manager = test_runner.subscription_manager.clone();
    
    // Spawn the subscription management task (simulated)
    let subscription_task = tokio::spawn(async move {
        while let Some(command) = jito_rx.recv().await {
            match command {
                SubscriptionCommand::Subscribe { mint } => {
                    println!("📥 Received subscribe command for mint: {}", mint);
                    
                    // Check if already subscribed
                    {
                        let manager = subscription_manager.lock().await;
                        if manager.is_subscribed(&mint) {
                            println!("⚠️  Already subscribed to mint: {}", mint);
                            continue;
                        }
                    }
                    
                    let (subscribe_message, request_id) = create_logs_subscribe_message(&mint);
                    
                    // Track the pending subscription
                    {
                        let mut manager = subscription_manager.lock().await;
                        manager.add_pending_subscription(request_id, mint.clone());
                    }
                    
                    // Send to mock Helius WebSocket
                    if let Err(e) = mock_helius_tx.send(subscribe_message.to_string()) {
                        println!("❌ Error sending to mock Helius: {}", e);
                        break;
                    }
                    
                    // Simulate subscription confirmation
                    sleep(Duration::from_millis(10)).await;
                    let subscription_id = 12345u64 + request_id; // Unique subscription ID
                    
                    {
                        let mut manager = subscription_manager.lock().await;
                        if let Some(confirmed_mint) = manager.complete_pending_subscription(request_id, subscription_id) {
                            println!("✅ Subscription confirmed for mint: {} with ID: {}", confirmed_mint, subscription_id);
                        }
                    }
                }
                SubscriptionCommand::Unsubscribe { mint } => {
                    println!("📥 Received unsubscribe command for mint: {}", mint);
                    
                    let subscription_id = {
                        let mut manager = subscription_manager.lock().await;
                        manager.remove_subscription(&mint)
                    };
                    
                    if let Some(subscription_id) = subscription_id {
                        let unsubscribe_message = create_logs_unsubscribe_message(subscription_id);
                        if let Err(e) = mock_helius_tx.send(unsubscribe_message.to_string()) {
                            println!("❌ Error sending unsubscribe to mock Helius: {}", e);
                            break;
                        }
                        println!("✅ Unsubscribed from mint: {} (subscription_id: {})", mint, subscription_id);
                    } else {
                        println!("⚠️  No active subscription found for mint: {}", mint);
                    }
                }
            }
        }
    });
    
    // Test 1: New Token Event
    println!("\n🧪 Test 1: New Token Event");
    let new_token_event = json!({
        "type": "new_token",
        "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    });
    
    let event = test_runner.simulate_jito_event(new_token_event).await?;
    assert!(event.is_some(), "New token event should be parsed");
    
    let helius_msg = test_runner.verify_helius_message("logsSubscribe", Some("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")).await?;
    assert_eq!(helius_msg["params"][1]["commitment"], "finalized");
    
    // Wait for subscription to be confirmed
    sleep(Duration::from_millis(50)).await;
    let subscriptions = test_runner.get_subscription_state().await;
    assert!(subscriptions.contains(&"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string()));
    
    println!("✅ Test 1 passed: New token subscription works correctly");
    
    // Test 2: Duplicate Subscription Prevention
    println!("\n🧪 Test 2: Duplicate Subscription Prevention");
    let duplicate_event = json!({
        "type": "new_token",
        "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    });
    
    test_runner.simulate_jito_event(duplicate_event).await?;
    
    // Should not receive another Helius message
    let result = timeout(Duration::from_millis(100), test_runner.mock_helius_rx.recv()).await;
    assert!(result.is_err(), "Should not receive duplicate subscription message");
    
    println!("✅ Test 2 passed: Duplicate subscription prevention works");
    
    // Test 3: Dev Sold Event
    println!("\n🧪 Test 3: Dev Sold Event");
    let dev_sold_event = json!({
        "type": "dev_sold",
        "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    });
    
    let event = test_runner.simulate_jito_event(dev_sold_event).await?;
    assert!(event.is_some(), "Dev sold event should be parsed");
    
    let helius_msg = test_runner.verify_helius_message("logsUnsubscribe", None).await?;
    assert!(helius_msg["params"][0].is_number(), "Should have subscription ID parameter");
    
    // Wait for unsubscription to be processed
    sleep(Duration::from_millis(50)).await;
    let subscriptions = test_runner.get_subscription_state().await;
    assert!(!subscriptions.contains(&"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string()));
    
    println!("✅ Test 3 passed: Dev sold unsubscription works correctly");
    
    // Test 4: Invalid Event
    println!("\n🧪 Test 4: Invalid Event Handling");
    let invalid_event = json!({
        "type": "unknown_event",
        "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    });
    
    let event = test_runner.simulate_jito_event(invalid_event).await?;
    assert!(event.is_none(), "Invalid event should not be parsed");
    
    // Should not receive any Helius message
    let result = timeout(Duration::from_millis(100), test_runner.mock_helius_rx.recv()).await;
    assert!(result.is_err(), "Should not receive message for invalid event");
    
    println!("✅ Test 4 passed: Invalid event handling works correctly");
    
    // Clean up
    drop(test_runner.jito_tx); // Close the channel to stop the subscription task
    let _ = timeout(Duration::from_secs(1), subscription_task).await;
    
    println!("\n🎉 All smoke tests passed successfully!");
    println!("{}", "=".repeat(50));
    
    Ok(())
}
