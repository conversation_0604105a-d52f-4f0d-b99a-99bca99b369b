# Pump.fun Bot Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Solana wallet private key (base58 encoded)
# This is used for transaction signing and wallet initialization
PRIVATE_KEY=your_private_key_here

# Jito Shredstream endpoint for real-time transaction data
# Default: http://127.0.0.1:9999 (local Jito node)
# Production: Use your Jito node endpoint
SHREDSTREAM_ENDPOINT=http://127.0.0.1:9999

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Time window for metrics calculation after creator sells (in seconds)
# This determines how long to track buy/sell activity after a creator sell event
# Default: 10 seconds
METRICS_WINDOW=10

# Time window for recent activity tracking (in seconds)
# This determines how long to keep transaction records for analysis
# Default: 30 seconds
RECENT_WINDOW=30

# Minimum number of instructions required for transaction success heuristic
# Successful transactions typically have compute budget + main instruction
# Default: 2
MIN_INSTRUCTION_COUNT=2

# Minimum number of accounts required for transaction success heuristic
# Valid Pump.fun transactions typically involve 8+ accounts
# Default: 8
MIN_ACCOUNT_COUNT=8

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: error, warn, info, debug, trace
# Default: info
LOG_LEVEL=info

# Log file path (optional, logs to console if not set)
# Default: /var/log/pump-bot.log
LOG_FILE=/var/log/pump-bot.log

# Enable structured JSON logging (true/false)
# Default: false (human-readable format)
JSON_LOGGING=false

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Token cleanup interval (in seconds)
# How often to remove old tokens from memory
# Default: 300 seconds (5 minutes)
TOKEN_CLEANUP_TIME=300

# Metrics output interval (in seconds)
# How often to output metrics to console/logs
# Default: 2 seconds
METRICS_OUTPUT_INTERVAL=2

# =============================================================================
# OPTIONAL FEATURES
# =============================================================================

# Enable health check endpoint (true/false)
# Default: false
ENABLE_HEALTH_CHECK=false

# Health check port (if enabled)
# Default: 8080
HEALTH_CHECK_PORT=8080

# Enable Prometheus metrics (true/false)
# Default: false
ENABLE_PROMETHEUS=false

# Prometheus metrics port (if enabled)
# Default: 9090
PROMETHEUS_PORT=9090
