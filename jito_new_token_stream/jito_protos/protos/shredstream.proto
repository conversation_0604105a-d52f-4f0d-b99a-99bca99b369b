syntax = "proto3";

package shredstream;

import "shared.proto";
import "google/protobuf/timestamp.proto";

message Heartbeat {
  // don't trust IP:PORT from tcp header since it can be tampered over the wire
  // `socket.ip` must match incoming packet's ip. this prevents spamming an unwitting destination
  shared.Socket socket = 1;

  // regions for shredstream proxy to receive shreds from
  // list of valid regions: https://jito-labs.gitbook.io/mev/systems/connecting/mainnet
  repeated string regions = 2;
}

message HeartbeatResponse {
  // client must respond within `ttl_ms` to keep stream alive
  uint32 ttl_ms = 1;
}

service Shredstream {
  // RPC endpoint to send heartbeats to keep shreds flowing
  rpc SendHeartbeat (Heartbeat) returns (HeartbeatResponse) {}
}

message TraceShred {
  // source region, one of: https://jito-labs.gitbook.io/mev/systems/connecting/mainnet
  string region = 1;
  // timestamp of creation
  google.protobuf.Timestamp created_at = 2;
  // monotonically increases, resets upon service restart
  uint32 seq_num = 3;
}


// Shredstream Proxy

service ShredstreamProxy {
  rpc SubscribeEntries(SubscribeEntriesRequest) returns (stream Entry);
}

message SubscribeEntriesRequest {
  // tbd: we may want to add filters here
}

message Entry {
  // the slot that the entry is from
  uint64 slot = 1;

  // Serialized bytes of Vec<Entry>: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html
  bytes entries = 2;
}