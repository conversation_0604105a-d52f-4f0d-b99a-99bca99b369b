[package]
name = "jito-shredstream-proxy"
version = { workspace = true }
description = "Fast path to receive shreds from <PERSON><PERSON>, forwarding to local consumers. See https://docs.jito.wtf/lowlatencytxnfeed/ for details."
authors = { workspace = true }
homepage = { workspace = true }
edition = { workspace = true }

[dependencies]
arc-swap = { workspace = true }
bincode = { workspace = true }
borsh = { workspace = true }
clap = { workspace = true }
crossbeam-channel = { workspace = true }
dashmap = { workspace = true }
env_logger = { workspace = true }
hostname = { workspace = true }
itertools = { workspace = true }
jito-protos = { workspace = true }
log = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
rand = { workspace = true }
reqwest = { workspace = true }
serde_json = { workspace = true }
signal-hook = { workspace = true }
solana-client = { workspace = true }
solana-entry = { workspace = true }
solana-ledger = { workspace = true }
solana-metrics = { workspace = true }
solana-net-utils = { workspace = true }
solana-perf = { workspace = true }
solana-sdk = { workspace = true }
solana-streamer = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
tonic = { workspace = true }
