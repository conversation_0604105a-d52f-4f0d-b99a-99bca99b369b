[package]
name = "pump-bot"
version = "0.1.0"
edition = "2021"
publish = false

[dependencies]
serde = "1.0.217"
solana-entry = { workspace = true }
solana-sdk = { workspace = true }
solana-transaction-status = { workspace = true }
solana-client = { workspace = true }
reqwest = { version = "0.11.23", features = ["blocking", "json"] }
tokio = { version = "1.0", features = ["full"] }
serde_json = "1.0"
bs58 = "0.4.0"
bincode = "1.3.3"
uuid = { version = "1.0", features = ["v4"] }
tonic = { version = "0.10", features = ["tls"] }
spl-associated-token-account = "=6.0.0"
spl-token = "=7.0.0"
jito-protos = { path = "../jito_protos" }
thiserror = "1"
chrono = "0.4"
base64 = "0.22.1"
hex = "0.4"
solana-trader-client-rust = { workspace = true }
solana-trader-proto = "=0.2.4"
dotenv = "0.15"
log = "0.4"
env_logger = "0.10"
tokio-tungstenite = { version = "0.20", features = ["native-tls"] }
futures-util = "0.3"
url = "2.4"
prost = "0.12"
ctrlc = "3.4"
tungstenite = "0.20"

[dev-dependencies]

[build-dependencies]
tonic-build = "0.10"
