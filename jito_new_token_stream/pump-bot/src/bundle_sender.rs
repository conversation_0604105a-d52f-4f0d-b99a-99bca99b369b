use std::{process::Command, sync::Arc};
use serde_json::{json, Value};
use solana_sdk::{signature::Keypair, transaction::VersionedTransaction};
use jito_protos::{
    auth::{auth_service_client::AuthServiceClient, Role},
    bundle::{
        bundle_result::Result as BundleResultType, rejected::Reason, Accepted, Bundle,
        BundleResult, InternalError, SimulationFailure, StateAuctionBidRejected,
        WinningBatchBidRejected,
    },
    convert::proto_packet_from_versioned_tx,
    searcher::{
        searcher_service_client::SearcherServiceClient, SendBundleRequest, SendBundleResponse,
    },
};
use tokio::runtime::Runtime;
use tonic::{
    codegen::{Body, Bytes, InterceptedService, StdError},
    transport,
    transport::{Channel, Endpoint},
    Response, Status, Streaming,
};
use std::sync::atomic::Ordering;
use std::sync::atomic::AtomicPtr;
use thiserror::Error;

const JITO_ENDPOINT_AMSTERDAM: &str = "https://amsterdam.mainnet.block-engine.jito.wtf/api/v1/bundles";
const JITO_ENDPOINT_FRUNKFURT: &str = "https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles";
const JITO_ENDPOINT_NY: &str = "https://ny.mainnet.block-engine.jito.wtf/api/v1/bundles";
const JITO_ENDPOINT_TOKYO: &str = "https://tokyo.mainnet.block-engine.jito.wtf";
const JITO_ENDPOINT_SLC: &str = "https://slc.mainnet.block-engine.jito.wtf";
const JITO_ENDPOINT_LONDON: &str = "https://london.mainnet.block-engine.jito.wtf";

pub enum Region {
    Amsterdam = 0,
    Frunkfurt,
    Ny,
    Tokyo,
    Slc,
    London
}
pub const JITO_ENDPOINTS: [&str; 6] = [
    JITO_ENDPOINT_AMSTERDAM,
    JITO_ENDPOINT_FRUNKFURT,
    JITO_ENDPOINT_NY,
    JITO_ENDPOINT_TOKYO,
    JITO_ENDPOINT_SLC,
    JITO_ENDPOINT_LONDON
];
#[derive(Debug, Error)]
pub enum BlockEngineConnectionError {
    #[error("transport error {0}")]
    TransportError(#[from] transport::Error),
    #[error("client error {0}")]
    ClientError(#[from] Status),
}

#[derive(Debug)]
pub struct BundleSender {
    pub req_client: Arc<reqwest::blocking::Client>,
    pub service_client: SearcherServiceClient<Channel>,
    pub jito_endpoint: String,
    pub tokio_runtime: tokio::runtime::Runtime
}

pub type BlockEngineConnectionResult<T> = Result<T, BlockEngineConnectionError>;

impl BundleSender {
    pub fn create() -> Self {
        // let region_idx = Self::get_nearest_region_idx();
        // let jito_endpoint = JITO_ENDPOINTS[region_idx].to_string();
        let jito_endpoint = JITO_ENDPOINT_NY.to_string();
        // println!("jito_endpoint {}", jito_endpoint);
        let tokio_runtime: tokio::runtime::Runtime = tokio::runtime::Builder::new_current_thread()
            .worker_threads(4)
            // .on_thread_start(move || renice_this_thread(rpc_niceness_adj).unwrap())
            .thread_name("sendBundleThread")
            .enable_all()
            .build()
            .expect("Runtime");

        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert("Host", reqwest::header::HeaderValue::from_static("ny.mainnet.block-engine.jito.wtf"));
        headers.insert(reqwest::header::CONTENT_TYPE, reqwest::header::HeaderValue::from_static("application/json"));
    
        let req_client = reqwest::blocking::Client::builder()
            .danger_accept_invalid_certs(true) // remove this in production!
            .default_headers(headers)
            .build().expect("");

        let jito_endpoint_clone = jito_endpoint.clone();
        let handle = std::thread::spawn(move || {
            let rt = tokio::runtime::Runtime::new().expect("getting runtime failed!");
            rt.block_on(
                Self::get_searcher_client_no_auth(&jito_endpoint_clone)
            ).expect("error getting searcher_client")
        });
    
        let service_client = handle.join().unwrap();
        
        
        BundleSender{
            req_client: Arc::new(req_client),
            service_client,
            jito_endpoint,
            tokio_runtime
        }
    }
    fn get_latency(endpoint: &str) -> Option<u128> {
        let splits: Vec<&str> = endpoint.split('/').collect();
        
        let domain = splits[2];
        let output = Command::new("ping")
            .arg("-c 1") // Send 1 packet
            .arg(domain)
            .output()
            .ok()?;
    
        let output_str = String::from_utf8_lossy(&output.stdout);
        
        // Extract time=XX ms from output
        output_str.lines().find_map(|line| {
            if let Some(start) = line.find("time=") {
                let time_str = &line[start + 5..].split_whitespace().next()?;
                let delayed = time_str.parse::<f64>().ok().map(|t| (t * 1000.0) as u128); // Convert to microseconds
                delayed
            } else {
                None
            }
        })
    }
    pub fn get_nearest_region_idx() -> usize {
        let mut min_latency = u128::MAX;
        let mut min_region_idx = 0;
        for (region, endpoint) in JITO_ENDPOINTS.iter().enumerate() {
            let latency = Self::get_latency(*endpoint).expect("error to get latency");
            if min_latency > latency {
                min_latency = latency;
                min_region_idx = region;
            }
        }
        min_region_idx
    }
    pub fn send_bundle_via_json_rpc(&self, versioned_txs: &[VersionedTransaction]) {
        let serialized_txs: Vec<Vec<u8>> = versioned_txs
            .iter()
            .map(|tx| bincode::serialize(tx).unwrap())
            .collect();
        let encoded_txs: Vec<String> = serialized_txs
            .iter()
            .map(|tx| bs58::encode(tx).into_string())
            .collect();
        let payload = json!({
            "jsonrpc": "2.0",
            "id": uuid::Uuid::new_v4().to_string(),
            "method": "sendBundle",
            "params": [encoded_txs]
        });
        let client = Arc::clone(&self.req_client);
        Self::send_bundle_self(client, payload, self.jito_endpoint.to_string());
        
    }
    pub fn send_bundle_self(client: Arc<reqwest::blocking::Client>, payload: Value, jito_endpoint: String) {
        let ip = "https://*************/api/v1/bundles";
        

        let res = client
            .post(ip)
            .json(&payload) // Serialize the payload to JSON
            .send();

        if res.is_ok() {
            println!("✅ Bundle sent successfully! Response: {:#?}", res.unwrap().text());
        } else {
            println!("❌ Bundle send failed! Error: {:#?}", res.unwrap().text());
        }
    }
    pub fn send_bundle_via_grpc_sync(
        &mut self,
        transactions: &[VersionedTransaction],
    ) {
        let service_client = &mut self.service_client;
        self.tokio_runtime.block_on(
            Self::send_bundle_via_grpc(transactions, service_client)    
        ).expect("error sending bundle via grpc");
        
    }
    pub async fn send_bundle_via_grpc(
        transactions: &[VersionedTransaction],
        service_client: &mut SearcherServiceClient<Channel>
    ) -> Result<Response<SendBundleResponse>, Status>
    // where
    //     T: tonic::client::GrpcService<tonic::body::BoxBody> + Send + 'static + Clone,
    //     T::Error: Into<StdError>,
    //     T::ResponseBody: Body<Data = Bytes> + Send + 'static,
    //     <T::ResponseBody as Body>::Error: Into<StdError> + Send,
    //     <T as tonic::client::GrpcService<tonic::body::BoxBody>>::Future: std::marker::Send,
    {
        
        // convert them to packets + send over
        let packets: Vec<_> = transactions
            .iter()
            .map(proto_packet_from_versioned_tx)
            .collect();

        service_client
            .send_bundle(SendBundleRequest {
                bundle: Some(Bundle {
                    header: None,
                    packets,
                }),
            })
            .await
    }
    
    pub async fn get_searcher_client_no_auth(
        block_engine_url: &str,
    ) -> BlockEngineConnectionResult<SearcherServiceClient<Channel>> {
        let searcher_channel = Self::create_grpc_channel(block_engine_url).await?;
        
        let searcher_client = SearcherServiceClient::new(searcher_channel);
        Ok(searcher_client)
    }
    
    pub async fn create_grpc_channel(url: &str) -> BlockEngineConnectionResult<Channel> {
        let mut endpoint = Endpoint::from_shared(url.to_string()).expect("invalid url");
        if url.starts_with("https") {
            endpoint = endpoint.tls_config(tonic::transport::ClientTlsConfig::new())?;
        }
        Ok(endpoint.connect().await?)
    }
    
}
