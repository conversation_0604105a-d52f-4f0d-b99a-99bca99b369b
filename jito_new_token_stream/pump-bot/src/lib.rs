use solana_client::rpc_client::SerializableTransaction;
use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_client::rpc_config::RpcTransactionConfig;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::transaction::VersionedTransaction;
use solana_sdk::signer::SignerError;
use solana_sdk::instruction::Instruction;
use solana_sdk::hash::Hash;
use solana_sdk::address_lookup_table::AddressLookupTableAccount;
use solana_sdk::signer::keypair::Keypair;
use solana_sdk::system_instruction::transfer;
use solana_sdk::signature::Signer;
use solana_sdk::instruction::AccountMeta;
use solana_sdk::signature;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::compute_budget::ComputeBudgetInstruction;

use solana_entry::entry::Entry;
use solana_transaction_status::UiTransactionEncoding;
use spl_token::instruction::close_account;
use spl_token::instruction::sync_native;
use std::collections::HashMap;
use std::fs::File;
use std::io::BufReader;
use std::str::FromStr;
use std::sync::atomic::AtomicPtr;
use std::sync::atomic::Ordering;
use std::sync::atomic::AtomicU64;
use std::time::Instant;
use std::sync::atomic::AtomicBool;
use spl_associated_token_account::get_associated_token_address;
use spl_associated_token_account::instruction::create_associated_token_account;
use solana_client::rpc_client::RpcClient;
use std::env;
use std::fs;
use std::sync::Arc;
use serde::{Deserialize};
use jito_protos::shredstream::{
    shredstream_proxy_client::ShredstreamProxyClient, SubscribeEntriesRequest,
};
use tokio::runtime::Runtime;
use std::time::{SystemTime, UNIX_EPOCH};
use chrono;
use reqwest::Client;
use std::sync::Mutex;
use std::net::SocketAddr;
use tokio::net::{TcpListener, TcpStream};
use tokio_tungstenite::accept_async;
use futures_util::{StreamExt, SinkExt};
use serde_json::json;

pub mod bundle_sender;
pub mod utils;
pub use bundle_sender::*;
pub use utils::*;

#[derive(Debug, Clone)]
pub struct MonitoredToken {
    pub mint_key: Pubkey,
    pub dev_user_key: Pubkey,
    pub curve: Pubkey,
    pub detected_at: u64, // slot when detected
    pub token_create_time: String, // timestamp when token was created
    pub dev_sold_time: Option<String>, // timestamp when dev sold (None if not sold yet)
}

pub struct PumpBot {
    pub signers: HashMap<Pubkey, String>,
    pub rpc_client: RpcClient,
    pub payer: Keypair,
    pub cu_ix: Instruction,
    pub cp_ix: Instruction,
    pub tip_ix: Instruction,

    pub bundle_sender: AtomicPtr<BundleSender>,
    pub blockhash: AtomicPtr<Hash>,
    pub mints: AtomicPtr<Vec<Pubkey>>,
    pub last_slot: AtomicU64,
    pub enabled: AtomicBool,
    pub monitored_tokens: Arc<Mutex<Vec<MonitoredToken>>>,
    pub websocket_clients: Arc<Mutex<Vec<tokio::sync::mpsc::UnboundedSender<String>>>>
}
impl PumpBot {
    pub const BUY_COMPUTE_UNIT: u32 = 150000;
    pub const BUY_COMPUTE_PRICE: u64 = 100_000_000;
    pub const TIP_AMOUNT: u64 = 21_000_000;
    pub const WHITELIST_FILE_PATH: &str = "wallet-monitor.json";

    
    // pub const SIGNER_KEY: &str = "BCagckXeMChUKrHEd6fKFA1uiWDtcmCXMsqaheLiUPJd";
    // pub const SIGNER_KEYS: [&str; 9] = 
    // [
    //     "BCagckXeMChUKrHEd6fKFA1uiWDtcmCXMsqaheLiUPJd",
    //     "6XykiketEjia5Mbg84ssgqKz8d6CtU3Djks9K9P2Tm3B",
    //     "9aitUhKGzisVtZcg2kQd6hTmAq8mfbMtN2cJe6Jn6g1M",

    //     "DfMxre4cKmvogbLrPigxmibVTTQDuzjdXojWzjCXXhzj",
    //     "DNi1isPxBRNAqE8HFw4F9por1ZACJwwtzduajpYC5rHX",
    //     "vb8XaDVMVvhKszZgRUWCdHg4yRExm4baZ23hdmaxJMT",
    //     "iBd9DeBTkKL6sffYRDHQvshAke5up253kq1HLNiG4D9",

    //     "********************************************",
    //     "Ek9LQQrw8xxNhXw1y5hE7EwiYaTPqDTMZGAu7T1gCAYV"
    // ];

    pub const MAX_SOL_COST: u64 = 20_000_000;
    pub const RENT_TOKEN_ACCOUNT: u64 = 2_039_280;
    pub const PUMP_PID: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
    pub const PUMP_MINT_INDEX: usize = 2;
    pub const PUMP_CURVE_INDEX: usize = 3;
    pub const PUMP_ATA_VAULT_INDEX: usize = 4;
    pub const PUMP_USER_INDEX: usize = 6;

    pub const PUMP_DEV_MINT_INDEX: usize = 0;
    pub const PUMP_DEV_CURVE_INDEX: usize = 2;
    pub const PUMP_DEV_USER_INDEX: usize = 7;
    pub const PUMP_AMOUNT_POS: usize = 8;
    pub const LAUNCH_IX_DISCRIMINATOR: [u8; 8] = [0x18, 0x1e, 0xc8, 0x28, 0x05, 0x1c, 0x07, 0x77];
    pub const BUY_IX_DISCRIMINATOR: [u8; 8] = [0x52, 0xe1, 0x77, 0xe7, 0x4e, 0x1d, 0x2d, 0x46];
    pub const SELL_IX_DISCRIMINATOR: [u8; 8] = [0x5d, 0x58, 0x3c, 0x22, 0x5b, 0x12, 0x56, 0xc5];

    // Alternative discriminators for backward compatibility
    pub const BUY_IX_DISCRIMINATOR_ALT: [u8; 8] = [0x66, 0x06, 0x3d, 0x12, 0x01, 0xda, 0xeb, 0xea];
    pub const SELL_IX_DISCRIMINATOR_ALT: [u8; 8] = [0x33, 0xe6, 0x85, 0xa4, 0x01, 0x7f, 0x83, 0xad];
    pub const BUY_IX_DISCRIMINATOR_ALT2: [u8; 8] = [0xfa, 0xea, 0x0d, 0x7b, 0xd5, 0x9c, 0x13, 0xec];
    pub const SELL_IX_DISCRIMINATOR_ALT2: [u8; 8] = [0x2c, 0x77, 0xaf, 0xda, 0xc7, 0x4d, 0xc4, 0xeb];
    pub const SELL_IX_DISCRIMINATOR_ALT3: [u8; 8] = [0x95, 0x27, 0xde, 0x9b, 0xd3, 0x7c, 0x98, 0x1a];
    
    pub const BLOOM_PID: &str = "b1oomGGqPKGD6errbyfbVMBuzSC8WtAAYo8MwNafWW1";
    pub const BLOOM_MINT_INDEX: usize = 11;
    pub const BLOOM_CURVE_INDEX: usize = 7;
    pub const BLOOM_ATA_VAULT_INDEX: usize = 12;
    pub const BLOOM_PUMP_IX_POS: usize = 53;
    pub const BLOOM_PUMP_AMOUNT_POS: usize = 61;

    pub const PHONTON_PID: &str = "BSfD6SHZigAfDWSjzD5Q41jw8LmKwtmjskPH9XW1mrRW";
    pub const PHONTON_MINT_INDEX: usize = 3;
    pub const PHONTON_CURVE_INDEX: usize = 4;
    pub const PHONTON_ATA_VAULT_INDEX: usize = 5;
    pub const PHONTON_PUMP_AMOUNT_POS: usize = 16;
    pub const PHONTON_BUY_IX_DISCRIMINATOR: [u8; 8] = [0x52, 0xe1, 0x77, 0xe7, 0x4e, 0x1d, 0x2d, 0x46];
    pub const PHONTON_SELL_IX_DISCRIMINATOR: [u8; 8] = [0x5d, 0x58, 0x3c, 0x22, 0x5b, 0x12, 0x56, 0xc5];

    pub const SHREDSTREAM_ENDPOINT: &str = "http://127.0.0.1:9999";
    

    pub fn create() -> Self {
        let payer = Keypair::new();
        // println!("pubkey {}", payer.pubkey().to_string());
        let cu_ix = ComputeBudgetInstruction::set_compute_unit_limit(Self::BUY_COMPUTE_UNIT);
        let cp_ix = ComputeBudgetInstruction::set_compute_unit_price(Self::BUY_COMPUTE_PRICE);
        let tip_ix = get_tip_ix(&payer);
        let bundle_sender = BundleSender::create();
        let rpc_client = RpcClient::new_with_commitment(
            // String::from("https://api.mainnet-beta.solana.com"),
            String::from("https://mainnet.helius-rpc.com/?api-key=7bf98cdc-5ab3-470d-8956-b204c0b72e53"),
            CommitmentConfig::confirmed(),
        );
        let blockhash = Hash::default();//rpc_client.get_latest_blockhash().expect("error getting blockhash");
        let signers = Self::read_whitelist();
        Self {
            signers,
            rpc_client,
            payer,
            cu_ix,
            cp_ix,
            tip_ix,
            bundle_sender: AtomicPtr::new(Box::into_raw(Box::new(bundle_sender))),
            mints: AtomicPtr::new(Box::into_raw(Box::new(Vec::new()))),
            blockhash: AtomicPtr::new(Box::into_raw(Box::new(blockhash))),
            last_slot: AtomicU64::new(0),
            enabled: AtomicBool::new(true),
            monitored_tokens: Arc::new(Mutex::new(Vec::new())),
            websocket_clients: Arc::new(Mutex::new(Vec::new()))
        }
    }
    pub fn send_tx(&self, tx: &VersionedTransaction, skip_preflight: bool) {
        self.rpc_client.send_transaction_with_config(tx, RpcSendTransactionConfig {
            skip_preflight,
            ..RpcSendTransactionConfig::default()
        }).expect("sending tx error");
    }
    pub fn read_whitelist() -> HashMap<Pubkey, String> {
        let file = File::open(Self::WHITELIST_FILE_PATH).expect("error reading whitelist file");
        let reader = BufReader::new(file);

        // Parse the JSON object with wallet names as keys
        let items: HashMap<String, String> = serde_json::from_reader(reader).expect("reading json");

        let mut whitelists = HashMap::new();
        // Convert wallet addresses to Pubkey and store with names
        for (name, address) in items {
            let pubkey = Pubkey::from_str(&address).expect("reading whitelist failed!");
            whitelists.insert(pubkey, name);
        }
        whitelists
    }
    pub fn send_bundle_accumulated(&self, mint_key: &Pubkey, versioned_txs: &[VersionedTransaction]) {
        let bundle_sender = unsafe {
            &mut *self.bundle_sender.load(Ordering::SeqCst)
        };
        let mints = unsafe{&mut * self.mints.load(Ordering::SeqCst)};
        if !mints.contains(mint_key) {
            // bundle_sender.send_bundle_via_grpc_sync(versioned_txs);
            bundle_sender.send_bundle_via_json_rpc(versioned_txs);
            mints.push(mint_key.clone());
        }
    }
    pub fn parse_time_string(time_str: &str) -> Option<chrono::DateTime<chrono::Utc>> {
        // Parse format: "YYYY-MM-DD HH:MM:SS:mmm"
        if let Ok(datetime) = chrono::NaiveDateTime::parse_from_str(time_str, "%Y-%m-%d %H:%M:%S") {
            Some(chrono::DateTime::<chrono::Utc>::from_naive_utc_and_offset(datetime, chrono::Utc))
        } else {
            None
        }
    }

    pub fn is_token_older_than_minutes(token_time: &str, minutes: u64) -> bool {
        if let Some(token_datetime) = Self::parse_time_string(token_time) {
            let current_time = chrono::Utc::now();
            let duration = current_time.signed_duration_since(token_datetime);
            duration.num_minutes() >= minutes as i64
        } else {
            false // If we can't parse the time, assume it's not old enough to remove
        }
    }

    pub fn add_monitored_token(&self, mint_key: Pubkey, user_key: Pubkey, curve: Pubkey, slot: u64) {
        let current_time = Self::get_cur_time_ms();
        let monitored_token = MonitoredToken {
            mint_key,
            dev_user_key: user_key,
            curve,
            detected_at: slot,
            token_create_time: current_time.clone(),
            dev_sold_time: None,
        };
        
        if let Ok(mut tokens) = self.monitored_tokens.lock() {
            // Clean up tokens older than 3 minutes
            let initial_count = tokens.len();
            tokens.retain(|token| !Self::is_token_older_than_minutes(&token.token_create_time, 3));
            
            // Check if token already exists
            let exists = tokens.iter().any(|token| token.mint_key == monitored_token.mint_key);
            if !exists {
                tokens.push(monitored_token.clone());
            }
        }
    }
    pub fn get_monitored_tokens(&self) -> Vec<MonitoredToken> {
        if let Ok(tokens) = self.monitored_tokens.lock() {
            tokens.clone()
        } else {
            Vec::new()
        }
    }
    pub fn print_monitored_tokens(&self) {
        if let Ok(tokens) = self.monitored_tokens.lock() {
            if tokens.is_empty() {
                println!("📋 No monitored tokens found");
                return;
            }
            
            println!("📋 Monitored Tokens ({}):", tokens.len());
            for (i, token) in tokens.iter().enumerate() {
                let sold_status = match &token.dev_sold_time {
                    Some(sold_time) => format!("🕐 Sold: {}", sold_time),
                    None => "⏳ Not sold yet".to_string(),
                };
                println!("  {}. 🪙 Mint: {} | 👤 Dev: {} | 📈 Curve: {} | 🕐 Created: {} | {}", 
                    i + 1, token.mint_key, token.dev_user_key, token.curve, token.token_create_time, sold_status);
            }
        }
    }

    pub async fn process_entries(&self, entries: Vec<Entry>, slot: u64, start: u32, end: u32) -> bool {
        // println!("slot {}, start: {}, end: {}", slot, start, end);
        if self.enabled.load(Ordering::SeqCst) {
            let cur_slot = self.last_slot.load(Ordering::SeqCst);
            if cur_slot == 0 {
                self.update_blockhash(slot);
                return false;
            }
            
            // println!("entries.len {}, slot {}", entries.len(), slot);
            let pump_pid = Pubkey::from_str(Self::PUMP_PID).unwrap();
            let bloom_pid = Pubkey::from_str(Self::BLOOM_PID).unwrap();
            let phonton_pid = Pubkey::from_str(Self::PHONTON_PID).unwrap();
            for entry in entries.iter() {
                for tx in entry.transactions.iter() {
                    let static_keys = tx.message.static_account_keys();
                    /* if static_keys.contains(&pump_pid) {
                         println!("pump_pid: {}", static_keys.contains(&pump_pid));
                     }*/
                    
                    if static_keys.contains(&pump_pid){

                        let signature = tx.signatures.get(0).cloned().unwrap_or_default();

                        for ix in tx.message.instructions() {
                            let ix_pid: &Pubkey = &static_keys[ix.program_id_index as usize];
                            let is_new_token = ix.data.starts_with(&Self::LAUNCH_IX_DISCRIMINATOR);
                            let is_buy = ix.data.starts_with(&Self::BUY_IX_DISCRIMINATOR) ||
                                        ix.data.starts_with(&Self::BUY_IX_DISCRIMINATOR_ALT) ||
                                        ix.data.starts_with(&Self::BUY_IX_DISCRIMINATOR_ALT2);
                            let is_sell = ix.data.starts_with(&Self::SELL_IX_DISCRIMINATOR) ||
                                         ix.data.starts_with(&Self::SELL_IX_DISCRIMINATOR_ALT) ||
                                         ix.data.starts_with(&Self::SELL_IX_DISCRIMINATOR_ALT2) ||
                                         ix.data.starts_with(&Self::SELL_IX_DISCRIMINATOR_ALT3);

                            if is_sell {
                                // Check if we have enough accounts for sell instruction
                                if static_keys.len() <= 11 {
                                    continue;
                                }
                                
                                let mint_key = &static_keys[ix.accounts[Self::PUMP_MINT_INDEX] as usize];
                                let user_key = &static_keys[ix.accounts[Self::PUMP_USER_INDEX] as usize];
                                let curve = &static_keys[ix.accounts[Self::PUMP_CURVE_INDEX] as usize];
                                let associated_vault = &static_keys[ix.accounts[Self::PUMP_ATA_VAULT_INDEX] as usize];
                                let amount_sol = u64::from_le_bytes(ix.data[Self::PUMP_AMOUNT_POS..(Self::PUMP_AMOUNT_POS + 8)].try_into().expect("typecasting failed"));
                                
                                //println!("🚀 Sell detected at slot {} | 🪙 Mint: {} | 👤 User: {} | 📈 Curve: {} | 🔗 {}", 
                                //    slot, mint_key, user_key, curve, signature);

                                // Check if this is a dev selling a monitored token
                                let mut dev_sell_data = None;
                                
                                if let Ok(mut tokens) = self.monitored_tokens.lock() {
                                    for (i, token) in tokens.iter().enumerate() {
                                        let mint_match = token.mint_key == *mint_key;
                                        let user_match = token.dev_user_key == *user_key;
                                        
                                        if mint_match && user_match {
                                            let current_time = Self::get_cur_time_ms();
                                            let mint_address = mint_key.to_string();
                                            let token_create_time = tokens[i].token_create_time.clone();
                                            
                                            // Update the token's sold time
                                            tokens[i].dev_sold_time = Some(current_time.clone());
                                            
                                            // Collect data for async processing
                                            dev_sell_data = Some((
                                                mint_address,
                                                token_create_time,
                                                current_time,
                                                mint_key.clone(),
                                                user_key.clone(),
                                                curve.clone(),
                                                amount_sol,
                                                signature.clone(),
                                                slot
                                            ));
                                            
                                            break;
                                        }
                                    }
                                }
                                
                                // Process dev sell data outside of mutex scope
                                if let Some((mint_address, token_create_time, current_time, mint_key, user_key, curve, amount_sol, signature, slot)) = dev_sell_data {
                                    // Get risk data and check for Twitter URL
                                    match utils::get_risk_json(&mint_address).await {
                                        Ok(risk_data) => {
                                            // Check if token has Twitter URL
                                            if let Some(token_info) = risk_data.get("token") {
                                                if let Some(twitter_url) = token_info.get("twitter") {
                                                    if let Some(twitter_str) = twitter_url.as_str() {
                                                        if !twitter_str.is_empty() {
                                                            // Add Twitter info to WebSocket message
                                                            let dev_sell_msg = json!({
                                                                "type": "dev_sell",
                                                                "mint_key": mint_key.to_string(),
                                                                "slot": slot,
                                                                "dev_user_key": user_key.to_string(),
                                                                "curve": curve.to_string(),
                                                                "amount_sol": amount_sol,
                                                                // "signature": signature,
                                                                "token_create_time": token_create_time,
                                                                "dev_sold_time": current_time,
                                                                "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64,
                                                                "twitter_url": twitter_str
                                                            }).to_string();

                                                            println!("🚨 {} | 🔴 DEV SELL DETECTED! | 🪙 Mint: {} | 👤 Dev: {} | 💰 Amount: {} | 🔗 {} | 📅 Created: {} | 🕐 Sold: {}",
                                                                current_time, mint_key, user_key, amount_sol, signature, token_create_time, current_time);

                                                            println!("🐦 Twitter URL found: {}", twitter_str);
                                                            println!("� Broadcasting dev_sell event: {}", mint_key);

                                                            self.broadcast_to_websocket_clients(dev_sell_msg);
                                                        }
                                                    }
                                                } 
                                            }
                                        }
                                        Err(e) => {
                                            println!("❌ Failed to get risk data: {}", e);
                                        }
                                    }
                                }
                                
                                return true;
                            }

                            if is_new_token {
                                if static_keys.len() <= 11 {
                                    continue;
                                }

                                let mint_key = &static_keys[ix.accounts[Self::PUMP_DEV_MINT_INDEX] as usize];
                                let user_key = &static_keys[ix.accounts[Self::PUMP_DEV_USER_INDEX] as usize];
                                let curve = &static_keys[ix.accounts[Self::PUMP_DEV_CURVE_INDEX] as usize];
                                let associated_vault = &static_keys[ix.accounts[Self::PUMP_ATA_VAULT_INDEX] as usize];
                                let amount_sol = u64::from_le_bytes(ix.data[Self::PUMP_AMOUNT_POS..(Self::PUMP_AMOUNT_POS + 8)].try_into().expect("typecasting failed"));

                                let current_time = Self::get_cur_time_ms();
                                println!("🚀 New token detected at slot {} | 🪙 Mint: {} | 👤 Dev: {} | 📈 Curve: {} | 🔗 {} | 🕐 {}",
                                    slot, mint_key, user_key, curve, signature, current_time);

                                // Add to monitored tokens
                                self.add_monitored_token(
                                    mint_key.clone(),
                                    user_key.clone(),
                                    curve.clone(),
                                    slot
                                );

                                // Emit new_token event for Helius monitoring bot
                                let new_token_msg = json!({
                                    "type": "new_token",
                                    "mint_key": mint_key.to_string(),
                                    "dev_user_key": user_key.to_string(),
                                    "curve": curve.to_string(),
                                    "slot": slot,
                                    "token_create_time": current_time,
                                    "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64
                                }).to_string();

                                println!("📡 Broadcasting new_token event: {}", mint_key);
                                self.broadcast_to_websocket_clients(new_token_msg);

                            }
                            

                        }
                    }
                    
                }
            }

            self.update_blockhash(slot);
        }
        return false;
    }
    pub fn get_signer(&self, keys: &[Pubkey]) -> Option<(Pubkey, String)>{
        for key in keys {
            if let Some(name) = self.signers.get(key) {
                return Some((key.clone(), name.clone()));
            }
        }
        None
    }
    pub fn make_and_send_buy(&self, mint_key: &Pubkey, signer_key: &Pubkey, curve: &Pubkey, associated_vault: &Pubkey, detect_amount: u64, slot: u64, buy_detected: bool) {
        // Self::make_and_send_buy_org(&self, mint_key, signer_key, curve, associated_vault, detect_amount, slot, buy_detected);
        Self::make_and_send_buy_via_jup(&self, mint_key, signer_key, curve, associated_vault, detect_amount, slot, buy_detected);
    }
    pub fn make_and_send_buy_org(&self, mint_key: &Pubkey, signer_key: &Pubkey, curve: &Pubkey, associated_vault: &Pubkey, detect_amount: u64, slot: u64, buy_detected: bool) {
        let blockhash = unsafe{ &* self.blockhash.load(Ordering::SeqCst)};
        let ata = get_associated_token_address(&self.payer.pubkey(), mint_key);
        let ata_ix = get_ata_ix(mint_key, &self.payer);
        let buy_ix = get_buy_ix(mint_key, &self.payer, curve, associated_vault, &ata, slot);
        // println!("buy ix {:#?}", buy_ix);
        let tx = get_versioned_tx(&self.payer, &[self.cu_ix.clone(), self.cp_ix.clone(), ata_ix, buy_ix, self.tip_ix.clone()], &[], blockhash.clone(), &vec![&self.payer]).expect("getting tx failed!");
        let signature = tx.get_signature().clone();
        self.send_bundle_accumulated(mint_key, &[tx]);

        self.enabled.store(false, Ordering::SeqCst);
        let cur_time_ms = Self::get_cur_time_ms();
        println!("user {}", self.payer.pubkey().to_string());

        if buy_detected {
            
            println!("signer {} has bought amount {} of token {} at slot {}, Current time: {}", signer_key.to_string(), detect_amount, mint_key.to_string(), slot, cur_time_ms);
        }
        else {
            println!("signer {} has sold amount {} of token {} at slot {}, Current time: {}", signer_key.to_string(), detect_amount, mint_key.to_string(), slot, cur_time_ms);
        }

        println!("tx id: {}", signature);
    }
    pub fn make_and_send_buy_via_jup(&self, mint_key: &Pubkey, signer_key: &Pubkey, curve: &Pubkey, associated_vault: &Pubkey, detect_amount: u64, slot: u64, buy_detected: bool) {
        let blockhash = unsafe{ &* self.blockhash.load(Ordering::SeqCst)};
        let wsol_mint = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
        let mint_ata = get_associated_token_address(&self.payer.pubkey(), mint_key);
        let wsol_ata = get_associated_token_address(&self.payer.pubkey(), &wsol_mint);
        let wsol_temp_ta = Keypair::new();
        // let (wsol_jup_pda, _wsol_pda_bump) = get_jup_pda_token_account(&self.payer.pubkey(), &wsol_mint);
        let sol_transfer_ix = get_transfer_ix(&self.payer.pubkey(), &wsol_ata, Self::MAX_SOL_COST);
        // println!("wsol_ata {}", wsol_ata.to_string());
        // println!("wsol_pda_jup {}", wsol_pda_jup.to_string());
        let wsol_close_ix = get_close_ix(&wsol_ata, &self.payer.pubkey(), &self.payer.pubkey());
        // let create_jup_pda_ix = get_jup_pda_token_ix(&self.payer.pubkey(), &wsol_mint);

        let wsol_ata_ix = get_ata_ix(&wsol_mint, &self.payer);
        let wsol_sync_ix = get_sync_ix(&wsol_ata);
        let mint_ata_ix = get_ata_ix(mint_key, &self.payer);
        let buy_ix_via_jup = get_buy_ix_via_jup(mint_key, &self.payer, curve, associated_vault, &mint_ata, &wsol_ata, &wsol_temp_ta.pubkey());
        // let buy_ix = get_buy_ix(mint_key, &self.payer, curve, associated_vault, &ata, slot);
        // println!("buy ix {:#?}", buy_ix_via_jup.accounts);
        let tx = get_versioned_tx(
            &self.payer, 
            &[
                self.cu_ix.clone(), 
                self.cp_ix.clone(), 
                wsol_ata_ix,
                sol_transfer_ix, 
                wsol_sync_ix,
                // create_jup_pda_ix, 
                mint_ata_ix, 
                buy_ix_via_jup,
                wsol_close_ix,
                self.tip_ix.clone()
            ],
            &[], 
            blockhash.clone(),
            &vec![&self.payer, &wsol_temp_ta]
        ).expect("getting tx failed!");

        let signature = tx.get_signature().clone();
        self.send_bundle_accumulated(mint_key, &[tx]);
        // self.send_tx(&tx, true);

        self.enabled.store(false, Ordering::SeqCst);
        let cur_time_ms = Self::get_cur_time_ms();
        println!("user {}", self.payer.pubkey().to_string());

        if buy_detected {
            
            println!("signer {} has bought amount {} of token {} at slot {}, Current time: {}", signer_key.to_string(), detect_amount, mint_key.to_string(), slot, cur_time_ms);
        }
        else {
            println!("signer {} has sold amount {} of token {} at slot {}, Current time: {}", signer_key.to_string(), detect_amount, mint_key.to_string(), slot, cur_time_ms);
        }

        println!("tx id: {}", signature);
    }
    pub fn update_blockhash(&self, slot: u64) {
        let cur_slot = self.last_slot.load(Ordering::SeqCst);
        if cur_slot + 4 < slot {
            let blockhash_new_res = self.rpc_client.get_latest_blockhash();
            if blockhash_new_res.is_ok() {
                let blockhash_new = blockhash_new_res.expect("error getting blockhash");

                self.blockhash.store(Box::into_raw(Box::new(blockhash_new)), Ordering::SeqCst);
                
                self.last_slot.store(slot, Ordering::SeqCst);
                // println!("updated blockhash {:#?}, slot {}", blockhash_new, slot);
            }
            
        }
    }
    pub fn get_cur_time_ms() -> String {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards");

        let secs = now.as_secs();
        let millis = now.subsec_millis();
        let datetime = chrono::DateTime::from_timestamp(secs as i64, 0).unwrap();
        format!("{}:{:03}", datetime.format("%Y-%m-%d %H:%M:%S"), millis)
    }
    pub fn receive_entry_update(bot_param: Arc<PumpBot>) {
        let bot = Arc::clone(&bot_param);
        std::thread::spawn(move || {
            let rt = Runtime::new().unwrap();
            let result = rt.block_on(
                PumpBot::run_entry_update(bot)
            );
        });
        
    }
    pub async fn run_entry_update(bot: Arc<PumpBot>) {
        println!("🚀 Bot started! | 🔍 Monitoring wallet activity...");
        
        // Start WebSocket server in a separate task
        let bot_ws = Arc::clone(&bot);
        tokio::spawn(async move {
            bot_ws.start_websocket_server().await;
        });
        
        // Main connection loop with automatic reconnection
        loop {
            // Connect to ShredStream proxy with retry logic
            let mut client = loop {
                match ShredstreamProxyClient::connect(PumpBot::SHREDSTREAM_ENDPOINT).await {
                    Ok(client) => {
                        println!("✅ Connected to ShredStream proxy at {}", PumpBot::SHREDSTREAM_ENDPOINT);
                        break client;
                    }
                    Err(e) => {
                        eprintln!("❌ Failed to connect to ShredStream proxy at {}: {}",
                                 PumpBot::SHREDSTREAM_ENDPOINT, e);
                        eprintln!("🔄 Retrying in 5 seconds...");
                        eprintln!("💡 Make sure the jito-shredstream-proxy is running:");
                        eprintln!("   RUST_LOG=info ./target/release/jito-shredstream-proxy shredstream \\");
                        eprintln!("     --block-engine-url https://ny.mainnet.block-engine.jito.wtf \\");
                        eprintln!("     --auth-keypair auth.json \\");
                        eprintln!("     --desired-regions ny \\");
                        eprintln!("     --grpc-service-port 9999 \\");
                        eprintln!("     --dest-ip-ports 127.0.0.1:8001");
                        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    }
                }
            };

            let mut stream = loop {
                match client.subscribe_entries(SubscribeEntriesRequest {}).await {
                    Ok(response) => {
                        println!("✅ Successfully subscribed to entries stream");
                        break response.into_inner();
                    }
                    Err(e) => {
                        eprintln!("❌ Failed to subscribe to entries: {}", e);
                        eprintln!("🔄 Retrying in 3 seconds...");
                        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
                    }
                }
            };

            // Process messages from the stream
            loop {
                match stream.message().await {
                    Ok(Some(slot_entry)) => {
                        let start_deserialize = Instant::now();

                        let entries =
                            match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&slot_entry.entries) {
                                Ok(e) => e,
                                Err(e) => {
                                    println!("Deserialization failed with err: {e}");
                                    continue;
                                }
                            };
                        let duration_deserialize = start_deserialize.elapsed();
                        // println!("Time taken deserialize: {}ms", duration_deserialize.as_millis());

                        let bot_clone: Arc<PumpBot> = Arc::clone(&bot);
                        let slot = slot_entry.slot as u64;

                        tokio::spawn(async move {
                            let start = Instant::now();
                            if bot_clone.process_entries(entries, slot, 0, 0).await {
                                // Entry processed successfully
                            }
                        });

                        // let duration = start.elapsed();
                        // println!("Time taken: {}ms", duration.as_millis());
                    }
                    Ok(None) => {
                        println!("⚠️ Stream ended, attempting to reconnect...");
                        break; // Break inner loop to reconnect
                    }
                    Err(e) => {
                        eprintln!("❌ Error receiving message from stream: {}", e);
                        eprintln!("🔄 Attempting to reconnect in 5 seconds...");
                        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                        break; // Break inner loop to reconnect
                    }
                }
            }

            println!("🔄 Reconnecting to ShredStream proxy...");
            tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
        }
    }
    pub fn clear_monitored_tokens(&self) {
        if let Ok(mut tokens) = self.monitored_tokens.lock() {
            let count = tokens.len();
            tokens.clear();
            println!("🗑️ Cleared {} monitored tokens", count);
        }
    }
    pub fn get_monitored_tokens_count(&self) -> usize {
        if let Ok(tokens) = self.monitored_tokens.lock() {
            tokens.len()
        } else {
            0
        }
    }

    pub fn broadcast_to_websocket_clients(&self, message: String) {
        if let Ok(mut clients) = self.websocket_clients.lock() {
            // Remove disconnected clients
            clients.retain(|sender| {
                if let Err(_) = sender.send(message.clone()) {
                    false // Remove this client
                } else {
                    true // Keep this client
                }
            });
        }
    }

    pub async fn start_websocket_server(self: Arc<Self>) {
        let addr = "127.0.0.1:9005";
        let listener = TcpListener::bind(addr).await.expect("Failed to bind WebSocket server");
        println!("🌐 WebSocket server started on {}", addr);

        while let Ok((stream, addr)) = listener.accept().await {
            let bot_clone = Arc::clone(&self);
            tokio::spawn(async move {
                if let Err(e) = bot_clone.handle_websocket_connection(stream, addr).await {
                    eprintln!("WebSocket connection error: {}", e);
                }
            });
        }
    }

    async fn handle_websocket_connection(self: Arc<Self>, stream: TcpStream, addr: SocketAddr) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let ws_stream = accept_async(stream).await?;
        println!("🔗 New WebSocket connection from: {}", addr);

        // Add client to broadcast list
        let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel::<String>();
        {
            if let Ok(mut clients) = self.websocket_clients.lock() {
                clients.push(tx);
            }
        }

        // Send broadcast messages to this client
        let (mut ws_sender, _) = ws_stream.split();
        while let Some(msg) = rx.recv().await {
            if ws_sender.send(tokio_tungstenite::tungstenite::Message::Text(msg)).await.is_err() {
                break; // Client disconnected
            }
        }

        println!("🔌 WebSocket connection closed: {}", addr);
        Ok(())
    }
}


pub fn get_ata_ix(mint_key: &Pubkey, owner: &Keypair) -> Instruction {
    create_associated_token_account(&owner.pubkey(), &owner.pubkey(), mint_key, &spl_token::ID)
}
pub fn get_sync_ix(account_pubkey: &Pubkey) -> Instruction {
    sync_native(&spl_token::id(), account_pubkey).expect("getting sync ix error")
}
pub fn get_transfer_ix(from_pubkey: &Pubkey, to_pubkey: &Pubkey, lamports: u64) -> Instruction {
    transfer(from_pubkey, to_pubkey, lamports)
}
pub fn get_close_ix(close_address: &Pubkey, recipient_address: &Pubkey, authority_address: &Pubkey) -> Instruction {
    close_account(&spl_token::id(), close_address, recipient_address, authority_address, &[authority_address]).expect("close ix error")
}
pub fn get_jup_pda_token_account(user: &Pubkey, mint: &Pubkey) -> (Pubkey, u8) {
    let program_id = Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap();
    let seeds = &[
        b"token_account",
        user.as_ref(),
        mint.as_ref(),
    ];
    Pubkey::find_program_address(seeds, &program_id)
}
pub fn get_jup_pda_token_ix(user: &Pubkey, mint: &Pubkey) -> Instruction {
    let (pda, bump) = get_jup_pda_token_account(user, mint);
    let ix_data = [
        0x93, 0xf1, 0x7b, 0x64, 0xf4, 0x84, 0xae, 0x76,
        bump
    ];
    let main_acc_meta = vec![
        AccountMeta {
            pubkey: pda,
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: user.clone(),
            is_signer: true,
            is_writable: true,
        },
        AccountMeta {
            pubkey: mint.clone(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("11111111111111111111111111111111").unwrap(),
            is_signer: false,
            is_writable: false,
        },
    ];
    let program_id = Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap();
    Instruction::new_with_bytes(program_id, &ix_data, main_acc_meta)

}
pub fn get_buy_ix(
    mint_key: &Pubkey,
    owner: &Keypair,
    curve: &Pubkey,
    associated_vault: &Pubkey,
    ata: &Pubkey,
    slot: u64
) -> Instruction {
    // let slot_bytes = u64::MAX.to_le_bytes();
    // let max_sol_cost = 20_000_000u64.to_le_bytes();
    // let ix_data = [
    //     0x66, 0x06, 0x3d, 0x12, 0x01, 0xda, 0xeb, 0xea, 
    //     slot_bytes[0], slot_bytes[1], slot_bytes[2], slot_bytes[3], slot_bytes[4], slot_bytes[5], slot_bytes[6], slot_bytes[7], 
    //     max_sol_cost[0], max_sol_cost[1], max_sol_cost[2], max_sol_cost[3], max_sol_cost[4], max_sol_cost[5], max_sol_cost[6], max_sol_cost[7], 
    // ];

    let slot_bytes = (slot % 10000).to_le_bytes();
    let ix_data = [
        0x66, 0x06, 0x3d, 0x12, 0x01, 0xda, 0xeb, 0xea, 
        slot_bytes[0], slot_bytes[1], slot_bytes[2], slot_bytes[3], slot_bytes[4], slot_bytes[5], slot_bytes[6], slot_bytes[7], 
        0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00,
    ];
    let main_acc_meta = vec![
        AccountMeta {
            pubkey: Pubkey::from_str("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV").unwrap(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: mint_key.clone(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: curve.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: associated_vault.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: ata.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: owner.pubkey(),
            is_signer: true,
            is_writable: true,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("11111111111111111111111111111111").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("SysvarRent111111111111111111111111111111111").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap(),
            is_signer: false,
            is_writable: false,
        },
    ];
    let program_id = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap();
    Instruction::new_with_bytes(program_id, &ix_data, main_acc_meta)
}
pub fn get_buy_ix_via_jup(
    mint_key: &Pubkey,
    owner: &Keypair,
    curve: &Pubkey,
    associated_vault: &Pubkey,
    mint_ata: &Pubkey,
    wsol_ata: &Pubkey,
    wsol_temp_ta: &Pubkey,
) -> Instruction {

    let max_sol_cost = PumpBot::MAX_SOL_COST.to_le_bytes();
    let ix_data = [
        0xe5, 0x17, 0xcb, 0x97, 0x7a, 0xe3, 0xad, 0x2a, 
        0x01, 0x00, 0x00, 0x00, 0x31, 0x64, 0x00, 0x01, 
        max_sol_cost[0], max_sol_cost[1], max_sol_cost[2], max_sol_cost[3], max_sol_cost[4], max_sol_cost[5], max_sol_cost[6], max_sol_cost[7], 
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
        0x00, 0x00, 0x00
    ];
    let main_acc_meta = vec![
        
        AccountMeta {
            pubkey: Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: owner.pubkey(),
            is_signer: true,
            is_writable: true,
        },
        AccountMeta {
            pubkey: wsol_ata.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: mint_ata.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: mint_key.clone(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("6Wzuv7vLc6Vq8HJcHwwSCE9SKcdJiuoJmJm3EMFkWERN").unwrap(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap(),
            is_signer: false,
            is_writable: false,
        },





        AccountMeta {
            pubkey: Pubkey::from_str("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV").unwrap(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: mint_key.clone(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: curve.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: associated_vault.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: mint_ata.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: owner.pubkey(),
            is_signer: true,
            is_writable: true,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("11111111111111111111111111111111").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("SysvarRent111111111111111111111111111111111").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: wsol_ata.clone(),
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: wsol_temp_ta.clone(),
            is_signer: true,
            is_writable: true,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("J56q6nX15WHRLJcsGB6s1bjaiywrn8DqLLvLccz61cYx").unwrap(),
            is_signer: false,
            is_writable: false,
        },
    ];
    let program_id = Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap();
    Instruction::new_with_bytes(program_id, &ix_data, main_acc_meta)
}
pub fn get_tip_ix(owner: &Keypair) -> Instruction {
    transfer(
        &owner.pubkey(),
        &Pubkey::from_str("HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe").unwrap(),
        PumpBot::TIP_AMOUNT,
    )
}

pub fn get_versioned_tx(
    payer: &Keypair,
    tx_instructions: &[Instruction],
    lookuptables: &[AddressLookupTableAccount],
    blockhash: Hash,
    signers: &Vec<&Keypair>
) -> std::result::Result<VersionedTransaction, SignerError> {
    // let signers = vec![&payer];

    let versioned_message = solana_sdk::message::VersionedMessage::V0(
        solana_sdk::message::v0::Message::try_compile(
            &payer.pubkey(),
            tx_instructions,
            lookuptables,
            blockhash,
        )
        .unwrap(),
    );
    VersionedTransaction::try_new(versioned_message, signers)
}

// Add specific wallet monitoring constants
const EURIOS_WALLET: &str = "DfMxre4cKmvogbLrPigxmibVTTQDuzjdXojWzjCXXhzj";
const CUPSEY_WALLET: &str = "suqh5sHtr8HyJ7q8scBimULPkPpA557prMG47xCHQfK";

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::Value;
    use std::sync::mpsc;
    use tokio::time::{timeout, Duration};

    #[test]
    fn test_new_token_event_format() {
        // Test that new_token events have the correct format expected by Helius monitor
        let mint_key = Pubkey::new_unique();
        let user_key = Pubkey::new_unique();
        let curve_key = Pubkey::new_unique();
        let slot = 12345u64;
        let current_time = PumpBot::get_cur_time_ms();

        let new_token_msg = json!({
            "type": "new_token",
            "mint_key": mint_key.to_string(),
            "dev_user_key": user_key.to_string(),
            "curve": curve_key.to_string(),
            "slot": slot,
            "token_create_time": current_time,
            "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64
        });

        // Verify required fields for Helius monitor
        assert_eq!(new_token_msg["type"], "new_token");
        assert!(new_token_msg["mint_key"].is_string());
        assert!(!new_token_msg["mint_key"].as_str().unwrap().is_empty());

        // Verify it can be parsed as expected by Helius monitor
        let parsed: Value = serde_json::from_str(&new_token_msg.to_string()).unwrap();
        assert_eq!(parsed["type"], "new_token");
        assert_eq!(parsed["mint_key"], mint_key.to_string());
    }

    #[test]
    fn test_dev_sell_event_format() {
        // Test that dev_sell events have the correct format expected by Helius monitor
        let mint_key = Pubkey::new_unique();
        let user_key = Pubkey::new_unique();
        let curve_key = Pubkey::new_unique();
        let slot = 12345u64;
        let amount_sol = 1000000u64;
        let current_time = PumpBot::get_cur_time_ms();
        let twitter_url = "https://twitter.com/example";

        let dev_sell_msg = json!({
            "type": "dev_sell",
            "mint_key": mint_key.to_string(),
            "slot": slot,
            "dev_user_key": user_key.to_string(),
            "curve": curve_key.to_string(),
            "amount_sol": amount_sol,
            "token_create_time": current_time.clone(),
            "dev_sold_time": current_time,
            "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64,
            "twitter_url": twitter_url
        });

        // Verify required fields for Helius monitor
        assert_eq!(dev_sell_msg["type"], "dev_sell");
        assert!(dev_sell_msg["mint_key"].is_string());
        assert!(!dev_sell_msg["mint_key"].as_str().unwrap().is_empty());

        // Verify it can be parsed as expected by Helius monitor
        let parsed: Value = serde_json::from_str(&dev_sell_msg.to_string()).unwrap();
        assert_eq!(parsed["type"], "dev_sell");
        assert_eq!(parsed["mint_key"], mint_key.to_string());
    }

    #[test]
    fn test_monitored_token_lifecycle() {
        // Create a bot with minimal setup to avoid file dependencies
        let payer = Keypair::new();
        let cu_ix = ComputeBudgetInstruction::set_compute_unit_limit(PumpBot::BUY_COMPUTE_UNIT);
        let cp_ix = ComputeBudgetInstruction::set_compute_unit_price(PumpBot::BUY_COMPUTE_PRICE);
        let tip_ix = get_tip_ix(&payer);
        let bundle_sender = BundleSender::create();
        let rpc_client = RpcClient::new_with_commitment(
            String::from("https://api.mainnet-beta.solana.com"),
            CommitmentConfig::confirmed(),
        );
        let blockhash = Hash::default();
        let signers = HashMap::new(); // Empty signers for test

        let bot = PumpBot {
            signers,
            rpc_client,
            payer,
            cu_ix,
            cp_ix,
            tip_ix,
            bundle_sender: AtomicPtr::new(Box::into_raw(Box::new(bundle_sender))),
            mints: AtomicPtr::new(Box::into_raw(Box::new(Vec::new()))),
            blockhash: AtomicPtr::new(Box::into_raw(Box::new(blockhash))),
            last_slot: AtomicU64::new(0),
            enabled: AtomicBool::new(true),
            monitored_tokens: Arc::new(Mutex::new(Vec::new())),
            websocket_clients: Arc::new(Mutex::new(Vec::new()))
        };

        let mint_key = Pubkey::new_unique();
        let user_key = Pubkey::new_unique();
        let curve_key = Pubkey::new_unique();
        let slot = 12345u64;

        // Initially no monitored tokens
        assert_eq!(bot.get_monitored_tokens_count(), 0);

        // Add a token
        bot.add_monitored_token(mint_key, user_key, curve_key, slot);
        assert_eq!(bot.get_monitored_tokens_count(), 1);

        // Verify token details
        let tokens = bot.get_monitored_tokens();
        assert_eq!(tokens.len(), 1);
        assert_eq!(tokens[0].mint_key, mint_key);
        assert_eq!(tokens[0].dev_user_key, user_key);
        assert_eq!(tokens[0].curve, curve_key);
        assert_eq!(tokens[0].detected_at, slot);
        assert!(tokens[0].dev_sold_time.is_none());

        // Clear tokens
        bot.clear_monitored_tokens();
        assert_eq!(bot.get_monitored_tokens_count(), 0);
    }

    #[test]
    fn test_websocket_broadcast() {
        // Create a simple bot for testing broadcast functionality
        let payer = Keypair::new();
        let cu_ix = ComputeBudgetInstruction::set_compute_unit_limit(PumpBot::BUY_COMPUTE_UNIT);
        let cp_ix = ComputeBudgetInstruction::set_compute_unit_price(PumpBot::BUY_COMPUTE_PRICE);
        let tip_ix = get_tip_ix(&payer);
        let bundle_sender = BundleSender::create();
        let rpc_client = RpcClient::new_with_commitment(
            String::from("https://api.mainnet-beta.solana.com"),
            CommitmentConfig::confirmed(),
        );
        let blockhash = Hash::default();
        let signers = HashMap::new();

        let bot = Arc::new(PumpBot {
            signers,
            rpc_client,
            payer,
            cu_ix,
            cp_ix,
            tip_ix,
            bundle_sender: AtomicPtr::new(Box::into_raw(Box::new(bundle_sender))),
            mints: AtomicPtr::new(Box::into_raw(Box::new(Vec::new()))),
            blockhash: AtomicPtr::new(Box::into_raw(Box::new(blockhash))),
            last_slot: AtomicU64::new(0),
            enabled: AtomicBool::new(true),
            monitored_tokens: Arc::new(Mutex::new(Vec::new())),
            websocket_clients: Arc::new(Mutex::new(Vec::new()))
        });

        // Create a test message
        let test_msg = json!({
            "type": "new_token",
            "mint_key": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            "test": true
        }).to_string();

        // Test broadcast (should not panic even with no clients)
        bot.broadcast_to_websocket_clients(test_msg);

        // This test mainly verifies the broadcast function doesn't crash
        // In a real scenario, you'd set up actual WebSocket clients to test reception
    }
}
