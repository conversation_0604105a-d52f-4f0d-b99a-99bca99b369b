mod constants;
mod utils;

use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::transaction::VersionedTransaction;
use solana_sdk::signer::SignerError;
use solana_sdk::instruction::Instruction;
use solana_sdk::hash::Hash;
use solana_sdk::signature::Keypair;
use solana_sdk::address_lookup_table::AddressLookupTableAccount;
use solana_sdk::system_instruction::transfer;
use solana_sdk::signature::Signer;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_trader_client_rust::provider::http::HTTPClient;

use solana_entry::entry::Entry;
use spl_token::instruction::close_account;
use spl_token::instruction::sync_native;
use std::str::FromStr;
use std::sync::atomic::AtomicPtr;
use std::sync::atomic::Ordering;
use std::sync::atomic::AtomicU64;
use std::sync::atomic::AtomicBool;
use spl_associated_token_account::instruction::create_associated_token_account;
use solana_client::rpc_client::RpcClient;
use std::env;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use jito_protos::shredstream::{
    shredstream_proxy_client::ShredstreamProxyClient, SubscribeEntriesRequest,
};
use tokio::runtime::Runtime;
use std::time::{SystemTime, UNIX_EPOCH};
use chrono;
use reqwest::Client;
use std::sync::{Mutex, OnceLock};
use std::collections::{HashMap, HashSet};

use reqwest::header::{HeaderMap, HeaderValue, COOKIE, USER_AGENT};
use serde_json::Value;
use std::time::Instant;
use std::time::Duration;
use tokio::time;
use std::io::Write;

use crate::constants::{
    COMPUTE_UNIT_LIMIT, COMPUTE_UNIT_PRICE, CREATE_DISCRIMINATOR, DEFAULT_SWAP_AMOUNT,
    MINT_CURVE_INDEX, MINT_TOKEN_INDEX, MINT_USER_INDEX, PHOTON_FEE_AMOUNT, PUMP_BUY_IX_DISCRIMINATOR,
    PUMP_BUY_SELL_CURVE_INDEX, PUMP_BUY_SELL_MINT_INDEX, PUMP_BUY_SELL_USER_INDEX,
    PUMP_FUN_PROGRAM_ID, PUMP_SELL_IX_DISCRIMINATOR, RECENT_WINDOW, SHREDSTREAM_ENDPOINT,
    SLIPPAGE_BPS, SOL_TRACK_API_KEY, TOKEN_CLEANUP_TIME
};

use crate::utils::get_market_cap;

#[derive(Clone, Hash, Eq, PartialEq)]
pub struct TokenPair {
    pub mint_key: Pubkey,
    pub curve_key: Pubkey,
}

pub static SIGNALED_TOKENS: OnceLock<Mutex<HashSet<TokenPair>>> = OnceLock::new();

#[derive(Clone)]
pub struct MonitoredToken {
    pub mint_key: Pubkey,
    pub curve_key: Pubkey,
    pub detection_time: Instant,
    pub total_buy_amount: u64,
    pub total_sell_count: u64,
    pub total_sell_amount: u64,
    pub total_buyers: u64,
    pub total_sellers: u64,
    pub creator_address: Pubkey,
    pub creator_sold_time: Option<Instant>,
    pub recent_transactions: Vec<(Pubkey, Instant, u64, bool)>,  // (wallet, time, amount, is_buy)
    pub continuous_sells: u32,  // Track number of continuous sells
    pub continuous_sell_amounts: Vec<u64>,  // Track amounts of continuous sells
}

pub struct PumpBot {
    pub rpc_client: RpcClient,
    pub payer: Keypair,

    pub blockhash: AtomicPtr<Hash>,
    pub mints: AtomicPtr<Vec<Pubkey>>,
    pub last_slot: AtomicU64,
    pub enabled: AtomicBool,
    pub buy_sell_mode: AtomicBool,
    pub trade_amount: AtomicU64,
    pub mint_key_ptr: AtomicPtr<Pubkey>,
    pub curve_ptr: AtomicPtr<Pubkey>,
    pub associated_vault_ptr: AtomicPtr<Pubkey>,
    pub monitored_tokens: Mutex<HashMap<Pubkey, MonitoredToken>>,
}
impl PumpBot {
    

    pub fn create() -> Self {
        let key = env::var("PRIVATE_KEY").expect("PRIVATE_KEY is not set");
        let payer = Keypair::from_base58_string(&key);
        // println!("pubkey {}", payer.pubkey().to_string());

        let rpc_client = RpcClient::new_with_commitment(
            // String::from("https://api.mainnet-beta.solana.com"),
            String::from("https://mainnet.helius-rpc.com/?api-key=7bf98cdc-5ab3-470d-8956-b204c0b72e53"),
            CommitmentConfig::confirmed(),
        );
        let blockhash = Hash::default();//rpc_client.get_latest_blockhash().expect("error getting blockhash");
        Self {
            rpc_client,
            payer,
            mints: AtomicPtr::new(Box::into_raw(Box::new(Vec::new()))),
            blockhash: AtomicPtr::new(Box::into_raw(Box::new(blockhash))),
            last_slot: AtomicU64::new(0),
            enabled: AtomicBool::new(true),
            buy_sell_mode: AtomicBool::new(true),
            trade_amount: AtomicU64::new(0u64),
            mint_key_ptr: AtomicPtr::new(Box::into_raw(Box::new(Pubkey::default()))),
            curve_ptr: AtomicPtr::new(Box::into_raw(Box::new(Pubkey::default()))),
            associated_vault_ptr: AtomicPtr::new(Box::into_raw(Box::new(Pubkey::default()))),
            monitored_tokens: Mutex::new(HashMap::new()),
        }
    }

    pub fn parse_two_u64_fields(data: &[u8]) -> Option<(u64, u64)> {
        if data.len() >= 24 {
            let amount = u64::from_le_bytes(data[8..16].try_into().ok()?);
            let second = u64::from_le_bytes(data[16..24].try_into().ok()?);
            Some((amount, second))
        } else {
            None
        }
    }

    pub fn add_token_to_monitor(&self, mint_key: Pubkey, curve_key: Pubkey, creator_address: Pubkey) {
        let mut monitored = self.monitored_tokens.lock().unwrap();
        monitored.insert(
            mint_key,
            MonitoredToken {
                mint_key,
                curve_key,
                detection_time: Instant::now(),
                total_buy_amount: 0,
                total_sell_count: 0,
                total_sell_amount: 0,
                total_buyers: 0,
                total_sellers: 0,
                creator_address,
                creator_sold_time: None,
                recent_transactions: Vec::new(),
                continuous_sells: 0,
                continuous_sell_amounts: Vec::new(),
            },
        );
    }

    pub fn remove_token(&self, mint_key: &Pubkey) {
        let mut monitored = self.monitored_tokens.lock().unwrap();
        monitored.remove(mint_key);
    }

    pub async fn monitor_tokens(bot: Arc<Self>) {
        let mut last_cleanup_time = Instant::now();
        let mut last_log_time = Instant::now();

        loop {
            time::sleep(Duration::from_millis(300)).await;

            let now = Instant::now();

            // Cleanup old tokens every minute
            if now.duration_since(last_cleanup_time) >= Duration::from_secs(60) {
                let mut to_remove = Vec::new();
                {
                    let monitored = bot.monitored_tokens.lock().unwrap();
                    for (mint_key, token) in monitored.iter() {
                        if now.duration_since(token.detection_time) >= Duration::from_secs(TOKEN_CLEANUP_TIME) {
                            to_remove.push(*mint_key);
                        }
                    }
                }

                if !to_remove.is_empty() {
                    for mint_key in to_remove {
                        bot.remove_token(&mint_key);
                    }
                }
                last_cleanup_time = now;
            }
        }
    }

    /*pub async fn print_monitored_tokens(bot: Arc<Self>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        dotenv::dotenv().ok();

        loop {
            time::sleep(Duration::from_millis(300)).await;

            let monitored_snapshot = {
                let monitored = bot.monitored_tokens.lock().unwrap();
                monitored.clone()
            };

            for (mint_key, token) in monitored_snapshot {
                if token.total_sellers > 0 || token.total_buyers <= 4 {
                    continue;
                }

                let should_process = {
                    // Removed BOUGHT_TOKENS logic related to printing
                    true
                };

                if !should_process {
                    continue;
                }

                // Rest of the print_monitored_tokens function...
                // ... existing code ...
            }
        }
    }*/


    pub async fn process_entries(&self, entries: Vec<Entry>, slot: u64, start: u32, end: u32) -> bool {
        // println!("slot {}, start: {}, end: {}", slot, start, end);
        if self.enabled.load(Ordering::SeqCst) {
       
            let pump_pid = Pubkey::from_str(constants::PUMP_FUN_PROGRAM_ID).unwrap();
            let mut processed_count = 0;

            for entry in entries.iter() {
                for tx in entry.transactions.iter() {
                    let static_keys = tx.message.static_account_keys();
                    
                    if static_keys.contains(&pump_pid){
                        let signature = tx.signatures.get(0).cloned().unwrap_or_default();

                        for ix in tx.message.instructions() {
                            let ix_pid: &Pubkey = &static_keys[ix.program_id_index as usize];
                            
                            if ix_pid.eq(&pump_pid) {
                                processed_count += 1;
                                let is_new_token = ix.data.starts_with(&constants::CREATE_DISCRIMINATOR);
                                let is_buy = ix.data.starts_with(&constants::PUMP_BUY_IX_DISCRIMINATOR);
                                let is_sell = ix.data.starts_with(&constants::PUMP_SELL_IX_DISCRIMINATOR);

                                // Skip if instruction doesn't have enough accounts
                                if ix.accounts.len() <= constants::PUMP_BUY_SELL_USER_INDEX {
                                    continue;
                                }

                                // Check each account index individually
                                let mint_idx = ix.accounts[constants::PUMP_BUY_SELL_MINT_INDEX] as usize;
                                let curve_idx = ix.accounts[constants::PUMP_BUY_SELL_CURVE_INDEX] as usize;
                                let user_idx = ix.accounts[constants::PUMP_BUY_SELL_USER_INDEX] as usize;

                                // Skip if any index is out of bounds
                                if mint_idx >= static_keys.len() || 
                                   curve_idx >= static_keys.len() || 
                                   user_idx >= static_keys.len() {
                                    continue;
                                }

                                if is_sell {
                                    let mint_key = &static_keys[mint_idx];
                                    let curve_key = &static_keys[curve_idx];
                                    let user_key = &static_keys[user_idx];
                                    if let Some((amount, _min_sol_output)) = Self::parse_two_u64_fields(&ix.data) {
                                        let mut monitored = self.monitored_tokens.lock().unwrap();
                                        if let Some(token) = monitored.get_mut(mint_key) {
                                            token.total_sell_count += 1;
                                            // Check if this wallet has sold before by looking at recent transactions
                                            let has_sold_before = token.recent_transactions.iter()
                                                .any(|(wallet, _, _, is_buy)| wallet == user_key && !*is_buy);
                                            if !has_sold_before {
                                                token.total_sellers += 1;
                                            }

                                            // Check if this is a signaled token
                                            let is_signaled = if let Some(signaled_tokens) = SIGNALED_TOKENS.get() {
                                                let tokens = signaled_tokens.lock().unwrap();
                                                tokens.contains(&TokenPair {
                                                    mint_key: *mint_key,
                                                    curve_key: *curve_key,
                                                })
                                            } else {
                                                false
                                            };

                                            if is_signaled {
                                                // Increment continuous sells counter and track amount
                                                token.continuous_sells += 1;
                                                token.continuous_sell_amounts.push(amount);
                                                
                                                // Check for sell signal (3 continuous sells)
                                                if token.continuous_sells >= 3 {
                                                    // Log all transactions to file
                                                    let total_sell_amount: u64 = token.continuous_sell_amounts.iter().sum();
                                                    let date = chrono::Local::now().format("%Y-%m-%d").to_string();
                                                    let filename = format!("pump_transactions_{}.txt", date);
                                                    let tx_msg = format!("{:#?}", tx.message); 

                                                    if let Ok(mut file) = std::fs::OpenOptions::new()
                                                        .create(true)
                                                        .append(true)
                                                        .open(&filename)
                                                    {
                                                        let log_msg = format!(
                                                            "{} | Token: {} | Creator: {} | Last Sell Sig: {} | Total Sell Amount: {} | tx_msg: {}\n",
                                                            Self::get_cur_time_ms(),
                                                            mint_key,
                                                            token.creator_address,
                                                            signature,
                                                            total_sell_amount,
                                                            tx_msg
                                                        );
                                                        if let Err(e) = writeln!(file, "{}", log_msg) {
                                                            println!("Error writing to log file: {}", e);
                                                        }
                                                    }
                                                    
                                                    println!("{} 🚨 SELL SIGNAL | Token: {} | Creator: {} | Last Sell Sig: {} | Total Sell Amount: {}",
                                                        Self::get_cur_time_ms(),
                                                        mint_key,
                                                        token.creator_address,
                                                        signature,
                                                        total_sell_amount
                                                    );

                                                    // Remove token from signaled_tokens after printing
                                                    if let Some(signaled_tokens) = SIGNALED_TOKENS.get() {
                                                        let mut tokens = signaled_tokens.lock().unwrap();
                                                        tokens.remove(&TokenPair {
                                                            mint_key: *mint_key,
                                                            curve_key: *curve_key,
                                                        });
                                                    }
                                                }
                                            }

                                            // Check if creator sold
                                            if user_key.eq(&token.creator_address) {
                                                token.creator_sold_time = Some(Instant::now());
                                                // Reset recent transactions when creator sells
                                                token.recent_transactions.clear();
                                            }

                                            // Add sell transaction to recent transactions
                                            if let Some(creator_sold_time) = token.creator_sold_time {
                                                let now = Instant::now();
                                                if now.duration_since(creator_sold_time) <= Duration::from_secs(RECENT_WINDOW) {
                                                    token.recent_transactions.push((*user_key, now, amount, false));
                                                }
                                            }
                                        }
                                    }
                                }
                                if is_buy {
                                    let mint_key = &static_keys[mint_idx];
                                    let curve_key = &static_keys[curve_idx];
                                    let user_key = &static_keys[user_idx];
                                    if let Some((amount, _max_sol_cost)) = Self::parse_two_u64_fields(&ix.data) {
                                        let mut monitored = self.monitored_tokens.lock().unwrap();
                                        if let Some(token) = monitored.get_mut(mint_key) {
                                            // Reset continuous sells counter and amounts on any buy
                                            token.continuous_sells = 0;
                                            token.continuous_sell_amounts.clear();
                                            
                                            token.total_buy_amount += amount;
                                            // Check if this wallet has bought before by looking at recent transactions
                                            let has_bought_before = token.recent_transactions.iter()
                                                .any(|(wallet, _, _, is_buy)| wallet == user_key && *is_buy);
                                            if !has_bought_before {
                                                token.total_buyers += 1;
                                            }

                                            // Track recent transactions
                                            if let Some(creator_sold_time) = token.creator_sold_time {
                                                let now = Instant::now();
                                                if now.duration_since(creator_sold_time) <= Duration::from_secs(RECENT_WINDOW) {
                                                    // Add buy transaction
                                                    token.recent_transactions.push((*user_key, now, amount, true));
                                                    
                                                    // Calculate recent activity
                                                    let recent_window = now - Duration::from_secs(RECENT_WINDOW);
                                                    let recent_activity: Vec<_> = token.recent_transactions.iter()
                                                        .filter(|(_, time, _, _)| *time >= recent_window)
                                                        .collect();

                                                    // Calculate buy and sell amounts in recent window
                                                    let recent_buy_amount: u64 = recent_activity.iter()
                                                        .filter(|(_, _, _, is_buy)| *is_buy)
                                                        .map(|(_, _, amount, _)| amount)
                                                        .sum();

                                                    let recent_sell_amount: u64 = recent_activity.iter()
                                                        .filter(|(_, _, _, is_buy)| !*is_buy)
                                                        .map(|(_, _, amount, _)| amount)
                                                        .sum();

                                                    // Count buy and sell transactions
                                                    let recent_buy_count = recent_activity.iter()
                                                        .filter(|(_, _, _, is_buy)| *is_buy)
                                                        .count();

                                                    let recent_sell_count = recent_activity.iter()
                                                        .filter(|(_, _, _, is_buy)| !*is_buy)
                                                        .count();

                                                    // Log all transactions to file
                                                    let date = chrono::Local::now().format("%Y-%m-%d").to_string();
                                                    let filename = format!("pump_transactions_{}.txt", date);
                                                    if let Ok(mut file) = std::fs::OpenOptions::new()
                                                        .create(true)
                                                        .append(true)
                                                        .open(&filename) 
                                                    {

                                                        let tx_msg = format!("{:#?}", tx.message); // or use serde_json::to_string if supported

                                                        let log_msg = format!("{} | Token: {} | Creator: {} | Buy Amount: {} | Sell Amount: {} | Buy Count: {} | Sell Count: {} | TxMsg: {}\n",
                                                            Self::get_cur_time_ms(),
                                                            mint_key,
                                                            token.creator_address,
                                                            recent_buy_amount,
                                                            recent_sell_amount,
                                                            recent_buy_count,
                                                            recent_sell_count,
                                                            tx_msg
                                                        );
                                                        if let Err(e) = writeln!(file, "{}", log_msg) {
                                                            println!("Error writing to log file: {}", e);
                                                        }
                                                    }

                                                    if recent_buy_amount > recent_sell_amount && recent_buy_count >= 4 && recent_buy_count - 2 >= recent_sell_count {
                                                        // Check if token is already signaled before printing
                                                        let should_buy = if let Some(signaled_tokens) = SIGNALED_TOKENS.get() {
                                                            let mut tokens = signaled_tokens.lock().unwrap();
                                                            let token_pair = TokenPair {
                                                                mint_key: *mint_key,
                                                                curve_key: *curve_key,
                                                            };
                                                            if !tokens.contains(&token_pair) {
                                                                tokens.insert(token_pair);
                                                                true
                                                            } else {
                                                                false
                                                            }
                                                        } else {
                                                            false
                                                        };

                                                        if should_buy {
                                                            // Get risk information
                                                            let risk_info = match crate::utils::get_risk_json(&mint_key.to_string()).await {
                                                                Ok(info) => format!("Risk Info: {}", info),
                                                                Err(e) => format!("Failed to get risk info: {}", e)
                                                            };

                                                            println!("{} 🚨 BUY SIGNAL | Token: {} | Creator: {} | Last Buy Sig: {} | Recent Buy Amount: {} | Recent Sell Amount: {} | Buy Count: {} | Sell Count: {} | {}",
                                                                Self::get_cur_time_ms(),
                                                                mint_key,
                                                                token.creator_address,
                                                                signature,
                                                                recent_buy_amount,
                                                                recent_sell_amount,
                                                                recent_buy_count,
                                                                recent_sell_count,
                                                                risk_info
                                                            );
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                if is_new_token {
                                    // Skip if instruction doesn't have enough accounts for new token
                                    if ix.accounts.len() <= constants::MINT_USER_INDEX {
                                        continue;
                                    }

                                    // Check each account index individually for new token
                                    let mint_idx = ix.accounts[constants::MINT_TOKEN_INDEX] as usize;
                                    let curve_idx = ix.accounts[constants::MINT_CURVE_INDEX] as usize;
                                    let user_idx = ix.accounts[constants::MINT_USER_INDEX] as usize;

                                    // Skip if any index is out of bounds
                                    if mint_idx >= static_keys.len() || 
                                       curve_idx >= static_keys.len() || 
                                       user_idx >= static_keys.len() {
                                        continue;
                                    }

                                    let mint_key = &static_keys[mint_idx];
                                    let curve_key = &static_keys[curve_idx];
                                    let user_key = &static_keys[user_idx];

                                    self.add_token_to_monitor(*mint_key, *curve_key, *user_key);
                                    return true;
                                }
                            }
                        }
                    }
                    
                }
            }

        }
        return false;
    }
  
 
    pub fn get_cur_time_ms() -> String {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards");

        let secs = now.as_secs();
        let millis = now.subsec_millis();
        let datetime = chrono::DateTime::from_timestamp(secs as i64, 0).unwrap();
        format!("{}:{:03}", datetime.format("%Y-%m-%d %H:%M:%S"), millis)
    }
    pub fn receive_entry_update(bot_param: Arc<PumpBot>) {
        let bot = Arc::clone(&bot_param);
        std::thread::spawn(move || {
            let rt = Runtime::new().unwrap();
            let result = rt.block_on(
                PumpBot::run_entry_update(bot)
            );
        });
        
    }
    pub async fn run_entry_update(bot: Arc<PumpBot>) {
        println!("Program Started: {}", Self::get_cur_time_ms());
        SIGNALED_TOKENS.get_or_init(|| Mutex::new(HashSet::new()));

        let mut client = ShredstreamProxyClient::connect(constants::SHREDSTREAM_ENDPOINT)
            .await
            .unwrap();
        let mut stream = client
            .subscribe_entries(SubscribeEntriesRequest {})
            .await
            .unwrap()
            .into_inner();
    
        while let Some(slot_entry) = stream.message().await.unwrap() {
            // let start_deserialize = Instant::now();

            let entries =
                match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&slot_entry.entries) {
                    Ok(e) => e,
                    Err(e) => {
                        println!("Deserialization failed with err: {e}");
                        continue;
                    }
                };
            // let duration_deserialize = start_deserialize.elapsed();
            // println!("Time taken deserialize: {}ms", duration_deserialize.as_millis());

            let bot_clone:  Arc<PumpBot> = Arc::clone(&bot);
            let slot = slot_entry.slot as u64;
            
            tokio::spawn(async move {
                bot_clone.process_entries(entries, slot, 0, 0).await;
            });

            tokio::spawn(PumpBot::monitor_tokens(Arc::clone(&bot)));

           // tokio::spawn(PumpBot::print_monitored_tokens(Arc::clone(&bot)));
            
        }
    }
}


pub fn get_ata_ix(mint_key: &Pubkey, owner: &Keypair) -> Instruction {
    create_associated_token_account(&owner.pubkey(), &owner.pubkey(), mint_key, &spl_token::ID)
}
pub fn get_sync_ix(account_pubkey: &Pubkey) -> Instruction {
    sync_native(&spl_token::id(), account_pubkey).expect("getting sync ix error")
}
pub fn get_transfer_ix(from_pubkey: &Pubkey, to_pubkey: &Pubkey, lamports: u64) -> Instruction {
    transfer(from_pubkey, to_pubkey, lamports)
}
pub fn get_close_ix(close_address: &Pubkey, recipient_address: &Pubkey, authority_address: &Pubkey) -> Instruction {
    close_account(&spl_token::id(), close_address, recipient_address, authority_address, &[authority_address]).expect("close ix error")
}
pub fn get_jup_pda_token_account(user: &Pubkey, mint: &Pubkey) -> (Pubkey, u8) {
    let program_id = Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap();
    let seeds = &[
        b"token_account",
        user.as_ref(),
        mint.as_ref(),
    ];
    Pubkey::find_program_address(seeds, &program_id)
}



pub fn get_versioned_tx(
    payer: &Keypair,
    tx_instructions: &[Instruction],
    lookuptables: &[AddressLookupTableAccount],
    blockhash: Hash,
    signers: &Vec<&Keypair>
) -> std::result::Result<VersionedTransaction, SignerError> {
    // let signers = vec![&payer];

    let versioned_message = solana_sdk::message::VersionedMessage::V0(
        solana_sdk::message::v0::Message::try_compile(
            &payer.pubkey(),
            tx_instructions,
            lookuptables,
            blockhash,
        )
        .unwrap(),
    );
    VersionedTransaction::try_new(versioned_message, signers)
}
