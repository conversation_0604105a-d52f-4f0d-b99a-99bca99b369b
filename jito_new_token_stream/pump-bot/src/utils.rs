use reqwest::Client;
use reqwest::header::{HeaderMap, HeaderValue, COOKIE, USER_AGENT};
use serde_json::Value;
use serde::{Deserialize};

#[derive(Deserialize, Debug)]
struct AxiomResponse {
    twitter: Option<String>,
}

#[derive(Debug, Deserialize)]
struct Risk {
    level: Option<String>,
}

#[derive(Debug, Deserialize)]
struct RugCheckResponse {
    risks: Option<Vec<Risk>>,
}

#[derive(Debug, Deserialize)]
struct TwitterUserInfo {
    followers: u32,
    #[serde(rename = "userName")]
    username: String,
    name: String,
    #[serde(rename = "isBlueVerified")]
    is_blue_verified: bool,
}

#[derive(Deserialize)]
pub struct MarketCapResponse {
    pub marketCap: f64,
}

#[derive(Debug, Deserialize)]
struct JupiterPriceData {
    id: String,
    #[serde(rename = "type")]
    price_type: String,
    price: String,
}

#[derive(Debug, Deserialize)]
struct JupiterPriceResponse {
    data: std::collections::HashMap<String, JupiterPriceData>,
    timeTaken: f64,
}

pub async fn get_risk_json(
    token_address: &str,
) -> Result<Value, Box<dyn std::error::Error + Send + Sync>> {
    let url = format!("https://data.solanatracker.io/tokens/{}", token_address);

    let mut headers = HeaderMap::new();
    
    // Try to get API key from environment variable first, fallback to constant
    let api_key = std::env::var("SOL_TRACK_API_KEY")
        .unwrap_or_else(|_| "73155f80-4998-4182-90a0-e53b057ff94d".to_string());
    
    headers.insert("x-api-key", HeaderValue::from_str(&api_key)?);
    headers.insert(USER_AGENT, HeaderValue::from_static("rust-client"));

    let client = reqwest::Client::new();
    let resp = client.get(&url).headers(headers).send().await?;

    if resp.status().is_success() {
        let json: Value = resp.json().await?;
        Ok(json)
    } else {
        Err(format!("HTTP error: {}", resp.status()).into())
    }
}

pub async fn check_axiom_twitter_field(pair_address: &str) -> Result<bool, String> {
    let url = format!(
        "https://api2.axiom.trade/pair-info?pairAddress={}",
        pair_address
    );

    let cookie_str = "auth-refresh-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWZyZXNoVG9rZW5JZCI6IjU5NmFlZDNjLTdjMmEtNDJiMi04MzljLWQ4YzNjMzFlYzBhNiIsImlhdCI6MTczOTYwMzMwM30.yqQQPuOQ5_kujo3c_iqFsIybcQ_BnpB4RxYaRVGEH3c; ph_phc_7bPgugSDujyCK9a1776BMM9UMGTNl2bUxGyg2UJuykr_posthog=%7B%22distinct_id%22%3A%2201950234-160a-714f-b052-50c47b6b1497%22%2C%22%24sesid%22%3A%5B1739768331650%2C%2201951243-66c8-7b6e-bec0-a7c01acbada6%22%2C1739768161992%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Faxiom.trade%2F%40smcmatt%22%7D%7D; auth-access-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoZW50aWNhdGVkVXNlcklkIjoiYzRiZmNhOTEtYjhhMy00ZWUzLTgzMDktYWMxYTM2MzIyMzM0IiwiaWF0IjoxNzQ5NDQ3OTcyLCJleHAiOjE3NDk0NDg5MzJ9.jmLecNB5mt_ATpLjoTjZ1QDRO8b4-BTnnyX2dTKPng8";

    let mut headers = HeaderMap::new();
    headers.insert(COOKIE, HeaderValue::from_str(cookie_str).unwrap());

    let client = reqwest::Client::new();
    let resp = client
        .get(&url)
        .headers(headers)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = resp.status();
    if !status.is_success() {
        return Err(format!("API returned error status: {}", status));
    }

    let body: AxiomResponse = resp
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    Ok(body.twitter.is_some())
}

pub async fn get_rugcheck_risk_level(mint: &str) -> Result<u8, String> {
    let url = format!("https://api.rugcheck.xyz/v1/tokens/{}/report", mint);
    let client = Client::new();

    let response = client
        .get(&url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = response.status();
    let text = response.text().await.map_err(|e| format!("Read failed: {}", e))?;

    if !status.is_success() {
        return Ok(4) // Invalid mint or rugcheck API error
    }

    // Try parsing actual rugcheck response
    let data: RugCheckResponse = serde_json::from_str(&text)
        .map_err(|e| format!("Failed to parse JSON: {} - raw: {}", e, text))?;

    if let Some(risks) = data.risks {
        let mut has_warn = false;
        for risk in risks {
            if let Some(level) = &risk.level {
                if level == "danger" {
                    return Ok(2);
                } else if level == "warn" {
                    has_warn = true;
                }
            }
        }
        if has_warn {
            return Ok(1);
        }
    }

    Ok(0)
}

pub async fn get_twitter_followers(twitter_handle: &str) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
    let url = format!(
        "https://api8.axiom.trade/twitter-user-info?twitterHandle={}",
        twitter_handle
    );

    let cookie_str = "auth-refresh-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWZyZXNoVG9rZW5JZCI6IjU5NmFlZDNjLTdjMmEtNDJiMi04MzljLWQ4YzNjMzFlYzBhNiIsImlhdCI6MTczOTYwMzMwM30.yqQQPuOQ5_kujo3c_iqFsIybcQ_BnpB4RxYaRVGEH3c; ph_phc_7bPgugSDujyCK9a1776BMM9UMGTNl2bUxGyg2UJuykr_posthog=%7B%22distinct_id%22%3A%2201950234-160a-714f-b052-50c47b6b1497%22%2C%22%24sesid%22%3A%5B1739768331650%2C%2201951243-66c8-7b6e-bec0-a7c01acbada6%22%2C1739768161992%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Faxiom.trade%2F%40smcmatt%22%7D%7D; auth-access-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoZW50aWNhdGVkVXNlcklkIjoiYzRiZmNhOTEtYjhhMy00ZWUzLTgzMDktYWMxYTM2MzIyMzM0IiwiaWF0IjoxNzQ5NDQ3OTcyLCJleHAiOjE3NDk0NDg5MzJ9.jmLecNB5mt_ATpLjoTjZ1QDRO8b4-BTnnyX2dTKPng8";

    let mut headers = HeaderMap::new();
    headers.insert(COOKIE, HeaderValue::from_str(cookie_str).unwrap());

    let client = reqwest::Client::new();
    let resp = client
        .get(&url)
        .headers(headers)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = resp.status();
    if !status.is_success() {
        return Err(format!("API returned error status: {}", status).into());
    }

    let user_info: TwitterUserInfo = resp
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    Ok(user_info.followers)
}
/*
pub async fn send_slack_notification(message: &str) -> Result<(), Box<dyn std::error::Error>> {
    dotenv::dotenv().ok();
    let webhook_url = env::var("SLACK_WEBHOOK_URL").unwrap_or_default();

    let client = reqwest::Client::new();
    let payload = serde_json::json!({
        "text": format!("<@U035CSH4DFZ> {}", message),
        "username": "Solana Trader Bot",
        "icon_emoji": ":rocket:"
    });

    let res = client
        .post(webhook_url)
        .json(&payload)
        .send()
        .await?;

    if !res.status().is_success() {
        eprintln!("Failed to send Slack notification: {:?}", res.text().await?);
    }
    
    Ok(())
}
*/
pub async fn get_market_cap(mint_key: &str) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();
    let mut headers = HeaderMap::new();
    headers.insert(USER_AGENT, HeaderValue::from_static("Mozilla/5.0"));
    
    // Try to get API key from environment variable first, fallback to constant
    let api_key = std::env::var("SOL_TRACK_API_KEY")
        .unwrap_or_else(|_| "73155f80-4998-4182-90a0-e53b057ff94d".to_string());
    
    headers.insert("x-api-key", HeaderValue::from_str(&api_key)?);

    let url = format!("https://data.solanatracker.io/price?token={}", mint_key);
    let response = client
        .get(&url)
        .headers(headers)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()).into());
    }

    let market_cap_data: MarketCapResponse = response.json().await?;
    Ok(market_cap_data.marketCap)
}

pub async fn get_jupiter_price(token_id: &str) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();
    let mut headers = HeaderMap::new();
    headers.insert(USER_AGENT, HeaderValue::from_static("Mozilla/5.0"));

    let url = format!("https://lite-api.jup.ag/price/v2?ids={}", token_id);
    let response = client
        .get(&url)
        .headers(headers)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(format!("Jupiter API request failed with status: {}", response.status()).into());
    }

    let price_response: JupiterPriceResponse = response.json().await?;
    
    // Get the price data for the specific token
    if let Some(price_data) = price_response.data.get(token_id) {
        let price: f64 = price_data.price.parse()
            .map_err(|e| format!("Failed to parse price '{}': {}", price_data.price, e))?;
        Ok(price)
    } else {
        Err(format!("Token {} not found in Jupiter API response", token_id).into())
    }
}
