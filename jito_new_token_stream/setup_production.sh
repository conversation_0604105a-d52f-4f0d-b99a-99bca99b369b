#!/bin/bash

# Pump.fun Bot Production Setup Script
# This script sets up the complete production environment for the pump-bot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "🚀 Pump.fun Bot Production Setup"
echo "=================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_status $RED "❌ This script must be run as root or with sudo"
    echo "Usage: sudo ./setup_production.sh"
    exit 1
fi

# Check if we're in the correct directory
if [ ! -f "Cargo.toml" ]; then
    print_status $RED "❌ Error: Must be run from the jito project root directory"
    exit 1
fi

print_status $YELLOW "📋 Setting up production environment..."

# 1. Build the project
print_status $YELLOW "🔨 Building release binary..."
sudo -u ubuntu cargo build -p pump-tester --release
if [ $? -ne 0 ]; then
    print_status $RED "❌ Failed to build binary"
    exit 1
fi

# 2. Create environment file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status $YELLOW "📝 Creating environment file from example..."
    sudo -u ubuntu cp .env.example .env
    print_status $YELLOW "⚠️  Please edit .env with your configuration before starting the service"
fi

# 3. Set up logging
print_status $YELLOW "📁 Setting up logging..."
mkdir -p /var/log
touch /var/log/pump-bot.log
touch /var/log/pump-bot-health.log
chown ubuntu:ubuntu /var/log/pump-bot*.log
chmod 644 /var/log/pump-bot*.log

# 4. Install logrotate configuration
print_status $YELLOW "🔄 Installing logrotate configuration..."
cp pump-bot.logrotate /etc/logrotate.d/pump-bot
chmod 644 /etc/logrotate.d/pump-bot

# 5. Set up systemd service
print_status $YELLOW "⚙️  Installing systemd service..."
cp pump-bot.service /etc/systemd/system/
chmod 644 /etc/systemd/system/pump-bot.service
systemctl daemon-reload
systemctl enable pump-bot

# 6. Set proper permissions
print_status $YELLOW "🔒 Setting proper permissions..."
chown -R ubuntu:ubuntu /home/<USER>/work/jito
chmod +x /home/<USER>/work/jito/target/release/pump-tester
chmod +x /home/<USER>/work/jito/health_check.sh
chmod +x /home/<USER>/work/jito/run_tests.sh

# 7. Set up health check cron job
print_status $YELLOW "⏰ Setting up health check cron job..."
CRON_JOB="*/5 * * * * /home/<USER>/work/jito/health_check.sh >/dev/null 2>&1"
(crontab -u ubuntu -l 2>/dev/null | grep -v "health_check.sh"; echo "$CRON_JOB") | crontab -u ubuntu -

# 8. Run tests to verify everything works
print_status $YELLOW "🧪 Running tests to verify setup..."
sudo -u ubuntu ./run_tests.sh
if [ $? -ne 0 ]; then
    print_status $YELLOW "⚠️  Some tests failed, but continuing with setup..."
fi

print_status $GREEN "🎉 Production setup completed successfully!"
echo ""
print_status $BLUE "📋 Next Steps:"
echo "1. Edit /home/<USER>/work/jito/.env with your configuration:"
echo "   - Set your PRIVATE_KEY"
echo "   - Configure SHREDSTREAM_ENDPOINT if needed"
echo "   - Adjust monitoring windows if desired"
echo ""
echo "2. Start the service:"
echo "   sudo systemctl start pump-bot"
echo ""
echo "3. Check service status:"
echo "   sudo systemctl status pump-bot"
echo ""
echo "4. View logs:"
echo "   sudo journalctl -u pump-bot -f"
echo "   tail -f /var/log/pump-bot.log"
echo ""
print_status $BLUE "📊 Monitoring & Management:"
echo "  Service control:     sudo systemctl {start|stop|restart|status} pump-bot"
echo "  View logs:           sudo journalctl -u pump-bot -f"
echo "  Health check:        ./health_check.sh"
echo "  Run tests:           ./run_tests.sh"
echo "  Log files:           /var/log/pump-bot*.log"
echo ""
print_status $BLUE "🔧 Configuration Files:"
echo "  Environment:         /home/<USER>/work/jito/.env"
echo "  Service:             /etc/systemd/system/pump-bot.service"
echo "  Logrotate:           /etc/logrotate.d/pump-bot"
echo "  Health check cron:   */5 * * * * (runs every 5 minutes)"
echo ""
print_status $BLUE "📈 Health Check & Alerting:"
echo "  Set HEALTH_CHECK_WEBHOOK_URL for webhook alerts"
echo "  Set HEALTH_CHECK_EMAIL for email alerts"
echo "  Set HEALTH_CHECK_SLACK_WEBHOOK for Slack alerts"
echo ""
print_status $GREEN "✨ Your Pump.fun monitoring bot is ready for production!"
